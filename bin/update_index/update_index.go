package main

import (
	"fmt"
	"time"

	"github.com/spf13/viper"
	"github.com/wilsonce/rabbitmv_bot/dao/query"
	"github.com/wilsonce/rabbitmv_bot/dto"
	"github.com/wilsonce/rabbitmv_bot/initialize"
	"github.com/wilsonce/rabbitmv_bot/service"
	search_index_group "github.com/wilsonce/search-index-group"
	"go.uber.org/zap"
)

var CoroutineNum int32
var UpdateIndexChan chan int32
var searchIndex *search_index_group.SearchIndex

func main() {
	viper.SetConfigFile("./rabbitmv_bot.toml")
	viper.ReadInConfig()

	logger := initialize.InitLog()
	defer logger.Sync()

	initialize.InitDB()
	initialize.Q = query.Use(initialize.DB)

	CoroutineNum = viper.GetInt32("searchIndex.coroutineNum")
	UpdateIndexChan = make(chan int32, CoroutineNum)
	var err error
	searchIndex, err = search_index_group.NewSearchIndex()

	if err != nil {
		logger.Error("初始化搜索引擎失败", zap.Any("err_info", err), zap.Any("searchIndex", searchIndex))
		return
	}
	updateIndex(func(vodId int32) {
		defer func() {
			<-UpdateIndexChan
		}()
		updateFn(vodId)
	})
}

func updateFn(vodId int32) {
	url := parseDetailUrl(vodId)
	notification := searchIndex.PublishUrl(url)
	for _, n := range notification {
		if n.Error != nil || n.Notification.HTTPStatusCode != 200 {
			initialize.Logger.Error("uri更新失败", zap.Any("err_info", n.Error), zap.Any("rsp_info", n.Notification))
			continue
		}
		initialize.Logger.Info("uri更新成功", zap.Any("rsp_info", n.Notification))
	}
}

func updateIndex(updateFn func(int32)) error {
	vodService := service.NewVodService()
	page := 1
	for {
		now := time.Now()
		start := time.Date(now.Year(), now.Month(), now.Day(), 0, 0, 0, 0, now.Location()).Unix()
		end := now.Unix()
		vodDto := dto.VodDto{
			StartVodTime: int32(start),
			EndVodTime:   int32(end),
			Page:         page,
		}
		list, _, err := vodService.GetDayUpdateVodList(vodDto)
		if err != nil {
			// 如果是查询本身出错，记录具体的错误信息
			initialize.Logger.Error("获取更新数据失败", zap.Error(err))
			return err
		}
		if len(list) == 0 {
			// 如果查询成功但没有数据，记录此信息
			initialize.Logger.Info("今日无更新数据，停止获取")
			// 既然是按页获取，没有数据就表示当前时间范围内没有更多数据了，可以跳出循环
			break
		}
		page++
		for _, v := range list {
			select {
			case UpdateIndexChan <- v.VodID:
				go updateFn(v.VodID)
			}
		}
	}
	// 循环正常结束（例如，没有更多数据时 break），返回 nil 表示没有错误
	return nil
}

func parseDetailUrl(vodId int32) string {
	tmpUrl := viper.GetString("searchIndex.detailUrl")
	return fmt.Sprintf(tmpUrl, vodId)
}
