package main

import (
	"gorm.io/driver/mysql"
	"gorm.io/gen"
	"gorm.io/gorm"
)

func main() {
	// 配置数据库连接
	db, _ := gorm.Open(mysql.Open("root:c27e624d27ab2e88@tcp(172.16.101.133:3306)/video?charset=utf8&parseTime=True&loc=Local"))

	// 生成器配置
	g := gen.NewGenerator(gen.Config{
		OutPath:           "../../dao/query",
		ModelPkgPath:      "../../dao/model",
		Mode:              gen.WithoutContext | gen.WithDefaultQuery | gen.WithQueryInterface,
		FieldNullable:     true,
		FieldWithIndexTag: true,
		FieldWithTypeTag:  true,
	})

	// 设置数据库连接
	g.UseDB(db)

	g.WithDataTypeMap(map[string]func(columnType gorm.ColumnType) (dataType string){
		"tinyint": func(columnType gorm.ColumnType) (dataType string) {
			return "int8"
		},
	})

	// 生成模型
	// g.GenerateModel("tho_vod")
	g.ApplyBasic(g.GenerateAllTable()...)
	// g.GenerateAllTable()

	// 执行生成
	g.Execute()
}
