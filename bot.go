package main

import (
	"context"
	"fmt"
	"math"
	"strconv"
	"strings"
	"time"

	tgbotapi "github.com/go-telegram-bot-api/telegram-bot-api/v5"
	"github.com/google/generative-ai-go/genai"
	"github.com/spf13/viper"
	"github.com/wilsonce/rabbitmv_bot/dao/query"
	"github.com/wilsonce/rabbitmv_bot/dto"
	"github.com/wilsonce/rabbitmv_bot/initialize"
	"github.com/wilsonce/rabbitmv_bot/service"
	"go.uber.org/zap"
	"google.golang.org/api/option"
)

func main() {
	viper.SetConfigFile("./rabbitmv_bot.toml")
	viper.ReadInConfig()
	logger := initialize.InitLog()
	defer logger.Sync()
	initialize.InitDB()
	//initialize.InitDao()
	//initialize.InitModel()
	initialize.Q = query.Use(initialize.DB)

	// Load Gemini API Key from config
	geminiAPIKey := viper.GetString("ai.gemini_api_key")
	if geminiAPIKey == "" {
		logger.Fatal("Gemini API key not found in configuration (ai.gemini_api_key)")
	}
	var geminiErr error
	service.GeminiClient, geminiErr = genai.NewClient(context.Background(), option.WithAPIKey(geminiAPIKey))
	if geminiErr != nil {
		logger.Fatal("Failed to create Gemini client", zap.Error(geminiErr))
	}

	bot, err := tgbotapi.NewBotAPI(viper.GetString("tg.token"))
	if err != nil {
		logger.Panic(err.Error())
	}

	bot.Debug = true
	u := tgbotapi.NewUpdate(0)
	u.Timeout = 60
	updates := bot.GetUpdatesChan(u)
	vodService := service.NewVodService()
	aiService := service.NewAiService()
	for update := range updates {
		if update.Message != nil {
			logger.Info("收到新消息", zap.Any("tg_message", update))
			// 处理命令
			if update.Message.IsCommand() {
				switch update.Message.Command() {
				case "rm": // 热门推荐
					msg := tgbotapi.NewMessage(update.Message.Chat.ID, "")
					msg.ParseMode = tgbotapi.ModeMarkdown
					msg.ReplyToMessageID = update.Message.MessageID
					msg.DisableWebPagePreview = true

					tmp, total, err := vodService.Recommend(1)
					if err != nil {
						tmp = service.WELCOM_HINT + "\n\n" + "未搜索到影片"
						msg.Text = tmp
						bot.Send(msg)
						continue
					}
					a := float64(total) / float64(service.PAGE_SIZE)
					totalPage := math.Ceil(a)
					tmp += fmt.Sprintf("\n当前第 %d 页，共 %d 页\n", 1, int64(totalPage))

					msg.ReplyMarkup = tgbotapi.NewInlineKeyboardMarkup(
						tgbotapi.NewInlineKeyboardRow(
							tgbotapi.NewInlineKeyboardButtonData("首页", "rm-page-1"),
							tgbotapi.NewInlineKeyboardButtonData("下一页", "rm-page-2"),
						),
						tgbotapi.NewInlineKeyboardRow(
							tgbotapi.NewInlineKeyboardButtonURL("兔子影视", service.VIDEO_HOST),
						),
					)
					msg.Text = tmp
					bot.Send(msg)
				case "tags": // 影视分类
					msg := tgbotapi.NewMessage(update.Message.Chat.ID, service.WELCOM_HINT)
					msg.ReplyToMessageID = update.Message.MessageID
					msg.ParseMode = tgbotapi.ModeMarkdown
					msg.DisableWebPagePreview = true
					msg.ReplyMarkup = vodService.GenerateTagskeyboard()
					bot.Send(msg)
				case "start":
					msg := tgbotapi.NewMessage(update.Message.Chat.ID, service.WELCOM_HINT+"\n\n输入你想要的电影，开始精彩生活")
					msg.ReplyToMessageID = update.Message.MessageID
					msg.ParseMode = tgbotapi.ModeMarkdown
					msg.DisableWebPagePreview = true
					bot.Send(msg)
				case "wb": // 微博热搜
					hotSearches, err := service.GetHotSearch()
					if err != nil {
						msg := tgbotapi.NewMessage(update.Message.Chat.ID, "获取微博热搜失败")
						msg.ReplyToMessageID = update.Message.MessageID
						bot.Send(msg)
						continue
					}

					var content strings.Builder
					content.WriteString("*📊 微博热搜榜*\n\n")
					for i, hs := range hotSearches {
						if i >= 50 { // 只显示前50条
							break
						}
						hotTag := ""
						if hs.IsHot {
							hotTag = "🔥"
						}
						content.WriteString(fmt.Sprintf("%d. [%s](https://s.weibo.com/weibo?q=%s)%s\n", i+1, hs.Word, hs.Word, hotTag))
					}
					content.WriteString("\n更新时间: " + time.Now().Format("2006-01-02 15:04:05"))

					msg := tgbotapi.NewMessage(update.Message.Chat.ID, content.String())
					msg.ReplyToMessageID = update.Message.MessageID
					msg.ParseMode = tgbotapi.ModeMarkdown
					msg.DisableWebPagePreview = true
					bot.Send(msg)
				default:
					msg := tgbotapi.NewMessage(update.Message.Chat.ID, service.WELCOM_HINT+"\n\n错误的命令")
					msg.ReplyToMessageID = update.Message.MessageID
					msg.ParseMode = tgbotapi.ModeMarkdown
					msg.DisableWebPagePreview = true
					bot.Send(msg)
				}
			} else { // 处理文本搜索
				rsp, aiErr := aiService.GeminiSendMessage(update.SentFrom().UserName, update.Message.Text)
				// 首先准备基础消息结构
				msg := tgbotapi.NewMessage(update.Message.Chat.ID, "")
				msg.ReplyToMessageID = update.Message.MessageID
				msg.ParseMode = tgbotapi.ModeMarkdown
				msg.DisableWebPagePreview = true // 确保这个属性也被设置

				if aiErr != nil {
					logger.Error("AI service error", zap.Error(aiErr), zap.String("user", update.SentFrom().UserName), zap.String("query", update.Message.Text))
					// 发送错误消息给用户
					msg.Text = "抱歉，AI 服务暂时遇到问题，请稍后再试。"
					bot.Send(msg)
					continue // AI出错，不再继续处理（不发送空rsp）
				}

				// AI 服务成功返回
				// AI 服务成功返回，设置消息文本为 AI 的响应
				// (注释掉的视频搜索逻辑保持不变)
				//tmp, total, err := vodService.GetVodList(update.Message.Text, 1)
				//if err != nil || total == 0 {
				//	tmp = service.WELCOM_HINT + "\n\n未找到影片"
				//	msg.Text = tmp
				//	bot.Send(msg)
				//	continue // 如果启用了视频搜索且未找到，则跳过
				//}
				//a := float64(total) / float64(service.PAGE_SIZE)
				//totalPage := int64(math.Ceil(a))
				//tmp += fmt.Sprintf("\n当前第 %d 页，共 %d 页\n", 1, totalPage)
				//if totalPage > 1 {
				//	msg.ReplyMarkup = tgbotapi.NewInlineKeyboardMarkup(tgbotapi.NewInlineKeyboardRow(
				//		tgbotapi.NewInlineKeyboardButtonData("首页", fmt.Sprintf("search-page-1-%s", update.Message.Text)),
				//		tgbotapi.NewInlineKeyboardButtonData("下一页", fmt.Sprintf("search-page-2-%s", update.Message.Text)),
				//	),
				//		tgbotapi.NewInlineKeyboardRow(
				//			tgbotapi.NewInlineKeyboardButtonURL("兔子影视", service.VIDEO_HOST),
				//		),
				//	)
				//}
				//msg.Text = tmp + "\n" + rsp // 如果启用了视频搜索，可能会合并结果

				// 设置最终的文本并发送
				msg.Text = rsp
				if rsp != "" { // 仅当 AI 有响应时发送
					bot.Send(msg)
				} else {
					// 如果 AI 响应为空，可以选择不发送或发送特定提示
					logger.Info("AI response was empty", zap.String("user", update.SentFrom().UserName), zap.String("query", update.Message.Text))
					// msg.Text = "AI 没有返回内容。" // 可选：发送提示
					// bot.Send(msg)
				}
			}
		} else if update.CallbackQuery != nil { // 处理键盘事件
			// Respond to the callback query, telling Telegram to show the user
			// a message with the data received.
			//callback := tgbotapi.NewCallback(update.CallbackQuery.ID, update.CallbackQuery.Data)
			//if _, err := bot.Request(callback); err != nil {
			//	panic(err)
			//}

			// And finally, send a message containing the data received.
			command := strings.Split(update.CallbackQuery.Data, "-")
			switch command[0] {
			case "rm":
				msg := tgbotapi.NewEditMessageText(update.CallbackQuery.Message.Chat.ID, update.CallbackQuery.Message.MessageID, "")
				msg.ParseMode = tgbotapi.ModeMarkdown
				msg.DisableWebPagePreview = true
				page, _ := strconv.Atoi(command[2])
				if page < 1 {
					page = 1
				}
				tmp, total, err := vodService.Recommend(page)
				if err != nil {
					tmp = service.WELCOM_HINT + "\n\n" + "未搜索到影片"
					msg.Text = tmp
					bot.Send(msg)
					continue
				}
				a := float64(total) / float64(service.PAGE_SIZE)
				totalPage := int64(math.Ceil(a))
				if int64(page) > totalPage {
					page = int(totalPage)
				}
				tmp += fmt.Sprintf("\n当前第 %d 页，共 %d 页\n", page, totalPage)

				pageKeyboard := []tgbotapi.InlineKeyboardButton{
					tgbotapi.NewInlineKeyboardButtonData("首页", "rm-page-1"),
				}
				if page > 1 {
					pageKeyboard = append(pageKeyboard, tgbotapi.NewInlineKeyboardButtonData("上一页", fmt.Sprintf("rm-page-%d", page-1)))
				}
				if int64(page) < totalPage {
					pageKeyboard = append(pageKeyboard, tgbotapi.NewInlineKeyboardButtonData("下一页", fmt.Sprintf("rm-page-%d", page+1)))
				}
				msg.ReplyMarkup = &tgbotapi.InlineKeyboardMarkup{
					InlineKeyboard: [][]tgbotapi.InlineKeyboardButton{
						pageKeyboard,
						[]tgbotapi.InlineKeyboardButton{
							tgbotapi.NewInlineKeyboardButtonURL("兔子影视", service.VIDEO_HOST),
						},
					},
				}
				msg.Text = tmp
				bot.Send(msg)
				continue

			case "search": // 文本搜索
				msg := tgbotapi.NewEditMessageText(update.CallbackQuery.Message.Chat.ID, update.CallbackQuery.Message.MessageID, "")
				msg.ParseMode = tgbotapi.ModeMarkdown
				msg.DisableWebPagePreview = true

				if len(command) < 4 {
					msg.Text = service.WELCOM_HINT + "\n\n" + "未搜索到影片"
					bot.Send(msg)
					continue
				}
				keywords := command[3]
				page, _ := strconv.Atoi(command[2])
				if page < 1 {
					page = 1
				}
				tmp, total, err := vodService.GetVodList(keywords, page)
				var totalPage int64
				if err != nil || total == 0 {
					tmp = service.WELCOM_HINT + "\n\n" + "未搜索到影片"
					msg.Text = tmp
					bot.Send(msg)
					continue
				} else {
					a := float64(total) / float64(service.PAGE_SIZE)
					totalPage = int64(math.Ceil(a))
					if int64(page) > totalPage {
						page = int(totalPage)
					}
					tmp += fmt.Sprintf("\n当前第 %d 页，共 %d 页\n", page, totalPage)
				}

				pageKeyboard := []tgbotapi.InlineKeyboardButton{
					tgbotapi.NewInlineKeyboardButtonData("首页", fmt.Sprintf("search-page-1-%s", keywords)),
				}
				if page > 1 {
					pageKeyboard = append(pageKeyboard, tgbotapi.NewInlineKeyboardButtonData("上一页", fmt.Sprintf("search-page-%d-%s", page-1, keywords)))
				}
				if int64(page) < totalPage {
					pageKeyboard = append(pageKeyboard, tgbotapi.NewInlineKeyboardButtonData("下一页", fmt.Sprintf("search-page-%d-%s", page+1, keywords)))
				}
				msg.ReplyMarkup = &tgbotapi.InlineKeyboardMarkup{
					InlineKeyboard: [][]tgbotapi.InlineKeyboardButton{
						pageKeyboard,
						[]tgbotapi.InlineKeyboardButton{
							tgbotapi.NewInlineKeyboardButtonURL("兔子影视", service.VIDEO_HOST),
						},
					},
				}
				msg.Text = tmp
				bot.Send(msg)
			case "cate":
				msg := tgbotapi.NewEditMessageText(update.CallbackQuery.Message.Chat.ID, update.CallbackQuery.Message.MessageID, "")
				msg.ParseMode = tgbotapi.ModeMarkdown
				msg.DisableWebPagePreview = true

				if len(command) < 3 {
					msg.Text = service.WELCOM_HINT + "\n\n" + "未搜索到影片"
					bot.Send(msg)
					continue
				}
				typeId, _ := strconv.Atoi(command[1])
				page, _ := strconv.Atoi(command[2])
				if page < 1 {
					page = 1
				}
				tmp, total, err := vodService.GetVodList2(dto.VodDto{
					TypeID: int32(typeId),
					Page:   page,
				})
				var totalPage int64
				if err != nil || total == 0 {
					tmp = service.WELCOM_HINT + "\n\n" + "未搜索到影片"
					msg.Text = tmp
					bot.Send(msg)
					continue
				} else {
					a := float64(total) / float64(service.PAGE_SIZE)
					totalPage = int64(math.Ceil(a))
					if int64(page) > totalPage {
						page = int(totalPage)
					}
					tmp += fmt.Sprintf("\n当前第 %d 页，共 %d 页\n", page, totalPage)
				}

				pageKeyboard := []tgbotapi.InlineKeyboardButton{
					tgbotapi.NewInlineKeyboardButtonData("首页", fmt.Sprintf("cate-%d-%d", typeId, 1)),
				}
				if page > 1 {
					pageKeyboard = append(pageKeyboard, tgbotapi.NewInlineKeyboardButtonData("上一页", fmt.Sprintf("cate-%d-%d", typeId, page-1)))
				}
				if int64(page) < totalPage {
					pageKeyboard = append(pageKeyboard, tgbotapi.NewInlineKeyboardButtonData("下一页", fmt.Sprintf("cate-%d-%d", typeId, page+1)))
				}
				msg.ReplyMarkup = &tgbotapi.InlineKeyboardMarkup{
					InlineKeyboard: [][]tgbotapi.InlineKeyboardButton{
						pageKeyboard,
						[]tgbotapi.InlineKeyboardButton{
							tgbotapi.NewInlineKeyboardButtonURL("兔子影视", service.VIDEO_HOST),
						},
					},
				}
				msg.Text = tmp
				bot.Send(msg)
			}
			//msg := tgbotapi.NewMessage(update.CallbackQuery.Message.Chat.ID, update.CallbackQuery.Data)
			//if _, err := bot.Send(msg); err != nil {
			//	logger.Error(err.Error())
			//}
		}
	}
}
