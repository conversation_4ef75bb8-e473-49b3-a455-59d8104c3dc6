package service

import (
	"encoding/json"
	"io"
	"net/http"
	"time"
)

type HotSearch struct {
	Word       string `json:"word"`
	Link       string `json:"link"`
	Hot        int    `json:"hot"`
	IsHot      bool   `json:"is_hot"`
	UpdateTime string `json:"update_time"`
}

type WeiboHotSearchResponse struct {
	Ok   int         `json:"ok"`
	Data []HotSearch `json:"data"`
}

func GetHotSearch() ([]HotSearch, error) {
	url := "https://weibo.com/ajax/side/hotSearch"
	client := &http.Client{
		Timeout: 10 * time.Second,
	}

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36")
	req.Header.Set("Accept", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, err
	}

	var result WeiboHotSearchResponse
	if err := json.Unmarshal(body, &result); err != nil {
		return nil, err
	}

	for i := range result.Data {
		result.Data[i].UpdateTime = time.Now().Format("2006-01-02 15:04:05")
	}

	return result.Data, nil
}
