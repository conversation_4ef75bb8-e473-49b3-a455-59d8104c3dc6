package service

import (
	"bytes"
	"context"
	"fmt"
	"strings"
	"sync"

	tgbotapi "github.com/go-telegram-bot-api/telegram-bot-api/v5"
	"github.com/wilsonce/rabbitmv_bot/dao/model"
	"github.com/wilsonce/rabbitmv_bot/dto"
	"github.com/wilsonce/rabbitmv_bot/initialize"
)

var WELCOM_HINT = "[欢迎来到兔子影视 >>> ](https://www.rabbitmv.top)"
var VIDEO_HOST = "https://www.rabbitmv.top"
var PAGE_SIZE = 20

var vodService *VodService
var vodServiceOnce sync.Once

func NewVodService() *VodService {
	vodServiceOnce.Do(func() {
		vodService = &VodService{}
	})
	return vodService
}

type VodService struct {
}

func (v *VodService) GetVodList2(vodQuery dto.VodDto) (string, int64, error) {
	if vodQuery.Page < 1 {
		vodQuery.Page = 1
	}
	pageSize := PAGE_SIZE
	offset := (vodQuery.Page - 1) * pageSize

	where := initialize.Q.ThoVod.WithContext(context.Background())
	if len(vodQuery.VodName) > 0 {
		where = where.Where(initialize.Q.ThoVod.VodName.Like("%" + vodQuery.VodName + "%")).
			Or(initialize.Q.ThoVod.VodActor.Like("%" + vodQuery.VodName + "%"))
	}
	if vodQuery.TypeID > 0 {
		where = where.Where(initialize.Q.ThoVod.TypeID.Eq(vodQuery.TypeID))
	}
	if vodQuery.StartVodTime > 0 {
		where = where.Where(initialize.Q.ThoVod.VodTime.Gte(vodQuery.StartVodTime))
	}
	if vodQuery.EndVodTime > 0 {
		where = where.Where(initialize.Q.ThoVod.VodTime.Lte(vodQuery.EndVodTime))
	}

	total, err := where.Count()
	if err != nil || 0 == total {
		return "", 0, err
	}
	list, err := where.
		Limit(pageSize).
		Offset(offset).
		Order(initialize.Q.ThoVod.VodTime.Desc()).
		Find()
	if err != nil {
		return "", 0, err
	}

	replyMessage, err := v.GenerateReplyMessage(list, vodQuery.Page)
	if err != nil {
		return "", 0, err
	}

	return replyMessage, total, nil
}

// 查询视频
func (v *VodService) GetVodList(vodName string, page int) (string, int64, error) {
	return v.GetVodList2(dto.VodDto{VodName: vodName, Page: page})
}

func (v *VodService) GenerateReplyMessage(data []*model.ThoVod, page int) (string, error) {
	tmp := bytes.NewBufferString(WELCOM_HINT + "\n\n")
	for k, v := range data {
		tmpVodName := strings.ReplaceAll(v.VodName, "[", "")
		tmpVodName = strings.ReplaceAll(tmpVodName, "]", "")
		tmp.WriteString(fmt.Sprintf("%d. [ %s](%s)\n", k+1+(page-1)*PAGE_SIZE, tmpVodName, fmt.Sprintf("%s/vod/detail/id/%d.html", VIDEO_HOST, v.VodID)))
	}
	return tmp.String(), nil
}

func (v *VodService) GenerateTagskeyboard() tgbotapi.InlineKeyboardMarkup {
	var numericKeyboard = tgbotapi.NewInlineKeyboardMarkup(
		//tgbotapi.NewInlineKeyboardRow(
		//	tgbotapi.NewInlineKeyboardButtonData("电影", "cate-1-1"),
		//	tgbotapi.NewInlineKeyboardButtonData("动漫", "cate-3-1"),
		//	tgbotapi.NewInlineKeyboardButtonData("电视剧", "cate-2-1"),
		//	tgbotapi.NewInlineKeyboardButtonData("综艺", "cate-4-1"),
		//),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("悬疑", "cate-40-1"),
			tgbotapi.NewInlineKeyboardButtonData("动作", "cate-6-1"),
			tgbotapi.NewInlineKeyboardButtonData("科幻", "cate-9-1"),
			tgbotapi.NewInlineKeyboardButtonData("战争", "cate-12-1"),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("喜剧", "cate-7-1"),
			tgbotapi.NewInlineKeyboardButtonData("国产剧", "cate-13-1"),
			tgbotapi.NewInlineKeyboardButtonData("香港剧", "cate-14-1"),
			tgbotapi.NewInlineKeyboardButtonData("韩国剧", "cate-15-1"),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("欧美剧", "cate-16-1"),
			tgbotapi.NewInlineKeyboardButtonData("日本剧", "cate-21-1"),
			tgbotapi.NewInlineKeyboardButtonData("台湾剧", "cate-20-1"),
			tgbotapi.NewInlineKeyboardButtonData("海外剧", "cate-22-1"),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("泰国剧", "cate-23-1"),
			tgbotapi.NewInlineKeyboardButtonData("国产动漫", "cate-29-1"),
			tgbotapi.NewInlineKeyboardButtonData("日韩动漫", "cate-30-1"),
			tgbotapi.NewInlineKeyboardButtonData("欧美动漫", "cate-31-1"),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("港台动漫", "cate-32-1"),
			tgbotapi.NewInlineKeyboardButtonData("海外动漫", "cate-33-1"),
			tgbotapi.NewInlineKeyboardButtonData("大陆综艺", "cate-24-1"),
			tgbotapi.NewInlineKeyboardButtonData("港台综艺", "cate-25-1"),
		),
		tgbotapi.NewInlineKeyboardRow(
			tgbotapi.NewInlineKeyboardButtonData("日韩综艺", "cate-26-1"),
			tgbotapi.NewInlineKeyboardButtonData("欧美综艺", "cate-27-1"),
			//tgbotapi.NewInlineKeyboardButtonData("大陆综艺", "cate-12-1"),
			//tgbotapi.NewInlineKeyboardButtonData("港台综艺", "cate-12-1"),
		),
	)

	return numericKeyboard
}

// 热门推荐
func (v *VodService) Recommend(page int) (string, int64, error) {
	if page < 1 {
		page = 1
	}
	offset := (page - 1) * PAGE_SIZE
	where := initialize.Q.ThoVod.WithContext(context.Background()).
		Where(initialize.Q.ThoVod.VodLevel.In(9, 1))
	total, err := where.Count()
	if err != nil || 0 == total {
		return "", 0, err
	}
	list, err := where.
		Limit(PAGE_SIZE).
		Offset(offset).
		Order(initialize.Q.ThoVod.VodTime.Desc()).
		Find()
	if err != nil {
		return "", 0, err
	}
	tmp, err := v.GenerateReplyMessage(list, page)
	if err != nil {
		return "", 0, err
	}
	return tmp, total, nil
}

func (v *VodService) GetDayUpdateVodList(vodQuery dto.VodDto) ([]*model.ThoVod, int64, error) {
	if vodQuery.Page < 1 {
		vodQuery.Page = 1
	}
	pageSize := PAGE_SIZE
	offset := (vodQuery.Page - 1) * pageSize
	where := initialize.Q.ThoVod.WithContext(context.Background())
	if len(vodQuery.VodName) > 0 {
		where = where.Where(initialize.Q.ThoVod.VodName.Like("%" + vodQuery.VodName + "%")).
			Or(initialize.Q.ThoVod.VodActor.Like("%" + vodQuery.VodName + "%"))
	}
	if vodQuery.TypeID > 0 {
		where = where.Where(initialize.Q.ThoVod.TypeID.Eq(vodQuery.TypeID))
	}
	if vodQuery.StartVodTime > 0 {
		where = where.Where(initialize.Q.ThoVod.VodTime.Gte(vodQuery.StartVodTime))
	}
	if vodQuery.EndVodTime > 0 {
		where = where.Where(initialize.Q.ThoVod.VodTime.Lte(vodQuery.EndVodTime))
	}

	total, err := where.Count()
	if err != nil || 0 == total {
		return nil, 0, err
	}
	list, err := where.
		Limit(pageSize).
		Offset(offset).
		Order(initialize.Q.ThoVod.VodTime.Desc()).
		Find()
	if err != nil {
		return nil, 0, err
	}

	return list, total, nil
}
