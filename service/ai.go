package service

import (
	"context"
	"fmt"
	"github.com/google/generative-ai-go/genai"
	"github.com/wilsonce/rabbitmv_bot/initialize"
	"go.uber.org/zap"
	"sync"
)

var GeminiClient *genai.Client

type AiService struct {
	geminiChat  sync.Map
	geminiModel *genai.GenerativeModel
}

var aiServiceOnce sync.Once
var aiService *AiService

func NewAiService() *AiService {
	aiServiceOnce.Do(func() {
		aiService = &AiService{
			geminiChat:  sync.Map{},
			geminiModel: GeminiClient.GenerativeModel("gemini-pro"),
		}
	})
	return aiService
}

func (a *AiService) GeminiStartChat(flag string) error {
	a.geminiChat.Store(flag, a.geminiModel.StartChat())
	return nil
}

func (a *AiService) GeminiSendMessage(flag string, question string) (string, error) {
	v, ok := a.geminiChat.Load(flag)
	if !ok {
		v = a.geminiModel.StartChat()
		a.geminiChat.Store(flag, v)
	}
	rsp, err := v.(*genai.ChatSession).SendMessage(context.Background(), genai.Text(question))
	if err != nil {
		return "", err
	}
	feedback := fmt.Sprintf("%s", rsp.Candidates[0].Content.Parts[0])
	initialize.Logger.Info("Ai message", zap.Any("ai", rsp))
	return feedback, nil
}
