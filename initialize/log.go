package initialize

import (
	"github.com/natefinch/lumberjack"
	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
	"sync"
)

var logOnce sync.Once
var Logger *zap.Logger

func InitLog() *zap.Logger {
	logOnce.Do(func() {
		//	rawJSON := []byte(`{
		// "level": "debug",
		// "encoding": "json",
		// "outputPaths": ["stdout", "./logs.txt"],
		// "errorOutputPaths": ["stderr"],
		// "encoderConfig": {
		//   "messageKey": "message",
		//   "levelKey": "level",
		//   "levelEncoder": "lowercase"
		// }
		//}`)
		//
		//	var cfg zap.Config
		//	cfg.EncoderConfig.EncodeTime = zapcore.ISO8601TimeEncoder
		//	cfg.EncoderConfig.EncodeDuration = zapcore.StringDurationEncoder
		//	if err := json.Unmarshal(rawJSON, &cfg); err != nil {
		//		panic(err)
		//	}
		//Logger, _ = zap.NewProduction(zap.AddCaller())

		writeSyncer := getLogWriter()
		encoder := getEncoder()
		core := zapcore.NewCore(encoder, writeSyncer, zapcore.DebugLevel)

		Logger = zap.New(core)
	})

	return Logger
}

func getEncoder() zapcore.Encoder {
	return zapcore.NewJSONEncoder(zap.NewProductionEncoderConfig())
}

func getLogWriter() zapcore.WriteSyncer {
	lumberJackLogger := &lumberjack.Logger{
		Filename:   "./rabbitmv_bot.log", // 文件位置
		MaxSize:    20,                   // 进行切割之前,日志文件的最大大小(MB为单位)
		MaxAge:     7,                    // 保留旧文件的最大天数
		MaxBackups: 10,                   // 保留旧文件的最大个数
		Compress:   false,                // 是否压缩/归档旧文件
	}
	return zapcore.AddSync(lumberJackLogger)
}
