package initialize

import (
	"os"
	"sync"
	"time"

	"github.com/spf13/viper"
	"github.com/wilsonce/rabbitmv_bot/dao/model"
	"github.com/wilsonce/rabbitmv_bot/dao/query"
	"gorm.io/driver/mysql"
	"gorm.io/gen"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"gorm.io/gorm/schema"
)

var DB *gorm.DB
var dbOnce sync.Once
var Q *query.Query

func InitDB() *gorm.DB {
	dbOnce.Do(func() {
		db, err := gorm.Open(mysql.New(mysql.Config{
			DSN:                       viper.GetString("mysql.dsn"), // DSN data source name
			DefaultStringSize:         256,                          // string 类型字段的默认长度
			DisableDatetimePrecision:  true,                         // 禁用 datetime 精度，MySQL 5.6 之前的数据库不支持
			DontSupportRenameIndex:    true,                         // 重命名索引时采用删除并新建的方式，MySQL 5.7 之前的数据库和 MariaDB 不支持重命名索引
			DontSupportRenameColumn:   true,                         // 用 `change` 重命名列，MySQL 8 之前的数据库和 MariaDB 不支持重命名列
			SkipInitializeWithVersion: false,                        // 根据当前 MySQL 版本自动配置
		}), &gorm.Config{
			Logger: logger.Default.LogMode(logger.Info),
		})

		if err != nil {
			Logger.Fatal("数据加连接失败")
			os.Exit(0)
		}
		DB = db
		sqlDb, _ := db.DB()
		sqlDb.SetMaxIdleConns(viper.GetInt("mysql.setMaxIdleConns"))
		sqlDb.SetMaxOpenConns(viper.GetInt("mysql.setMaxOpenConns"))
		sqlDb.SetConnMaxIdleTime(time.Hour)

		db.Config.NamingStrategy = schema.NamingStrategy{
			TablePrefix: "tho_",
		}
	})

	return DB
}

func InitDao() {
	g := gen.NewGenerator(gen.Config{
		OutPath:       "./dao", // output directory, default value is ./query
		Mode:          gen.WithDefaultQuery | gen.WithQueryInterface,
		FieldNullable: true,
	})

	// Initialize a *gorm.DB instance
	db := InitDB()

	// Use the above `*gorm.DB` instance to initialize the generator,
	// which is required to generate structs from db when using `GenerateModel/GenerateModelAs`
	g.UseDB(db)

	// Generate default DAO interface for those specified structs
	g.ApplyBasic(model.ThoVod{})

	// Generate default DAO interface for those generated structs from database
	//companyGenerator := g.GenerateModelAs("company", "MyCompany"),
	//	g.ApplyBasic(
	//		g.GenerateModel("users"),
	//		companyGenerator,
	//		g.GenerateModelAs("people", "Person",
	//			gen.FieldIgnore("deleted_at"),
	//			gen.FieldNewTag("age", `json:"-"`),
	//		),
	//	)

	// Execute the generator
	g.Execute()
}

func InitModel() {
	g := gen.NewGenerator(gen.Config{
		OutPath: "./model",
		Mode:    gen.WithoutContext | gen.WithDefaultQuery | gen.WithQueryInterface, // generate mode
	})

	// gormdb, _ := gorm.Open(mysql.Open("root:@(127.0.0.1:3306)/demo?charset=utf8mb4&parseTime=True&loc=Local"))
	db := InitDB()
	g.UseDB(db) // reuse your gorm db

	// Generate basic type-safe DAO API for struct `model.User` following conventions
	//g.GenerateModel("vod")
	g.GenerateAllTable()

	//g.ApplyBasic(
	//	// Generate structs from all tables of current database
	//	g.GenerateAllTable()...,
	//)
	// Generate the code
	g.Execute()
}
