package main

import (
	"fmt"
	// 使用正确的模块路径导入 service 包
	"github.com/wilsonce/rabbitmv_bot/service"
)

func main() {
	hotSearch, err := service.GetHotSearch()
	if err != nil {
		fmt.Printf("获取微博热搜失败: %v\n", err)
		return
	}

	fmt.Println("微博热搜榜:")
	fmt.Println("===============================")

	for i, item := range hotSearch {
		if item.IsHot {
			fmt.Printf("%d. 🔥 %s (热度: %d)\n", i+1, item.Word, item.Hot)
		} else {
			fmt.Printf("%d. %s (热度: %d)\n", i+1, item.Word, item.Hot)
		}
	}
}
