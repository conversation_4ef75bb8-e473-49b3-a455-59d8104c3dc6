// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameThoCjContent = "tho_cj_content"

// ThoCjContent mapped from table <tho_cj_content>
type ThoCjContent struct {
	ID     int32  `gorm:"column:id;type:int(10) unsigned;primaryKey;autoIncrement:true" json:"id"`
	Nodeid int32  `gorm:"column:nodeid;type:int(10) unsigned;not null;index:nodeid,priority:1" json:"nodeid"`
	Status int8   `gorm:"column:status;type:tinyint(1) unsigned;not null;index:status,priority:1;default:1" json:"status"`
	URL    string `gorm:"column:url;type:char(255);not null" json:"url"`
	Title  string `gorm:"column:title;type:char(100);not null" json:"title"`
	Data   string `gorm:"column:data;type:mediumtext;not null" json:"data"`
}

// TableName ThoCjContent's table name
func (*ThoCjContent) TableName() string {
	return TableNameThoCjContent
}
