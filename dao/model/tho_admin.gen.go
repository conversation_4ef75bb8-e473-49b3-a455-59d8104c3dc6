// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameThoAdmin = "tho_admin"

// ThoAdmin mapped from table <tho_admin>
type ThoAdmin struct {
	AdminID            int32  `gorm:"column:admin_id;type:smallint(6) unsigned;primaryKey;autoIncrement:true" json:"admin_id"`
	AdminName          string `gorm:"column:admin_name;type:varchar(30);not null;index:admin_name,priority:1" json:"admin_name"`
	AdminPwd           string `gorm:"column:admin_pwd;type:char(32);not null" json:"admin_pwd"`
	AdminRandom        string `gorm:"column:admin_random;type:char(32);not null" json:"admin_random"`
	AdminStatus        int8   `gorm:"column:admin_status;type:tinyint(1) unsigned;not null;default:1" json:"admin_status"`
	AdminAuth          string `gorm:"column:admin_auth;type:text;not null" json:"admin_auth"`
	AdminLoginTime     int32  `gorm:"column:admin_login_time;type:int(10) unsigned;not null" json:"admin_login_time"`
	AdminLoginIP       int32  `gorm:"column:admin_login_ip;type:int(10) unsigned;not null" json:"admin_login_ip"`
	AdminLoginNum      int32  `gorm:"column:admin_login_num;type:int(10) unsigned;not null" json:"admin_login_num"`
	AdminLastLoginTime int32  `gorm:"column:admin_last_login_time;type:int(10) unsigned;not null" json:"admin_last_login_time"`
	AdminLastLoginIP   int32  `gorm:"column:admin_last_login_ip;type:int(10) unsigned;not null" json:"admin_last_login_ip"`
}

// TableName ThoAdmin's table name
func (*ThoAdmin) TableName() string {
	return TableNameThoAdmin
}
