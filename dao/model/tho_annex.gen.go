// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameThoAnnex = "tho_annex"

// ThoAnnex mapped from table <tho_annex>
type ThoAnnex struct {
	AnnexID   int32  `gorm:"column:annex_id;type:int(10) unsigned;primaryKey;autoIncrement:true" json:"annex_id"`
	AnnexTime int32  `gorm:"column:annex_time;type:int(10) unsigned;not null;index:annex_time,priority:1" json:"annex_time"`
	AnnexFile string `gorm:"column:annex_file;type:varchar(255);not null;index:annex_file,priority:1" json:"annex_file"`
	AnnexSize int32  `gorm:"column:annex_size;type:int(10) unsigned;not null" json:"annex_size"`
	AnnexType string `gorm:"column:annex_type;type:varchar(8);not null;index:annex_type,priority:1" json:"annex_type"`
}

// TableName ThoAnnex's table name
func (*ThoAnnex) TableName() string {
	return TableNameThoAnnex
}
