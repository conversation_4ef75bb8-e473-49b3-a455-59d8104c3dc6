// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameThoMsg = "tho_msg"

// ThoMsg mapped from table <tho_msg>
type ThoMsg struct {
	MsgID      int32  `gorm:"column:msg_id;type:int(10) unsigned;primaryKey;autoIncrement:true" json:"msg_id"`
	UserID     int32  `gorm:"column:user_id;type:int(10) unsigned;not null;index:user_id,priority:1" json:"user_id"`
	MsgType    int8   `gorm:"column:msg_type;type:tinyint(1) unsigned;not null" json:"msg_type"`
	MsgStatus  int8   `gorm:"column:msg_status;type:tinyint(1) unsigned;not null" json:"msg_status"`
	MsgTo      string `gorm:"column:msg_to;type:varchar(30);not null" json:"msg_to"`
	MsgCode    string `gorm:"column:msg_code;type:varchar(10);not null;index:msg_code,priority:1" json:"msg_code"`
	MsgContent string `gorm:"column:msg_content;type:varchar(255);not null" json:"msg_content"`
	MsgTime    int32  `gorm:"column:msg_time;type:int(10) unsigned;not null;index:msg_time,priority:1" json:"msg_time"`
}

// TableName ThoMsg's table name
func (*ThoMsg) TableName() string {
	return TableNameThoMsg
}
