// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameThoUser = "tho_user"

// ThoUser mapped from table <tho_user>
type ThoUser struct {
	UserID            int32  `gorm:"column:user_id;type:int(10) unsigned;primaryKey;autoIncrement:true" json:"user_id"`
	GroupID           int32  `gorm:"column:group_id;type:smallint(6) unsigned;not null;index:type_id,priority:1" json:"group_id"`
	UserName          string `gorm:"column:user_name;type:varchar(30);not null;index:user_name,priority:1" json:"user_name"`
	UserPwd           string `gorm:"column:user_pwd;type:varchar(32);not null" json:"user_pwd"`
	UserNickName      string `gorm:"column:user_nick_name;type:varchar(30);not null" json:"user_nick_name"`
	UserQq            string `gorm:"column:user_qq;type:varchar(16);not null" json:"user_qq"`
	UserEmail         string `gorm:"column:user_email;type:varchar(30);not null" json:"user_email"`
	UserPhone         string `gorm:"column:user_phone;type:varchar(16);not null" json:"user_phone"`
	UserStatus        int8   `gorm:"column:user_status;type:tinyint(1) unsigned;not null" json:"user_status"`
	UserPortrait      string `gorm:"column:user_portrait;type:varchar(100);not null" json:"user_portrait"`
	UserPortraitThumb string `gorm:"column:user_portrait_thumb;type:varchar(100);not null" json:"user_portrait_thumb"`
	UserOpenidQq      string `gorm:"column:user_openid_qq;type:varchar(40);not null" json:"user_openid_qq"`
	UserOpenidWeixin  string `gorm:"column:user_openid_weixin;type:varchar(40);not null" json:"user_openid_weixin"`
	UserQuestion      string `gorm:"column:user_question;type:varchar(255);not null" json:"user_question"`
	UserAnswer        string `gorm:"column:user_answer;type:varchar(255);not null" json:"user_answer"`
	UserPoints        int32  `gorm:"column:user_points;type:int(10) unsigned;not null" json:"user_points"`
	UserPointsFroze   int32  `gorm:"column:user_points_froze;type:int(10) unsigned;not null" json:"user_points_froze"`
	UserRegTime       int32  `gorm:"column:user_reg_time;type:int(10) unsigned;not null;index:user_reg_time,priority:1" json:"user_reg_time"`
	UserRegIP         int32  `gorm:"column:user_reg_ip;type:int(10) unsigned;not null" json:"user_reg_ip"`
	UserLoginTime     int32  `gorm:"column:user_login_time;type:int(10) unsigned;not null" json:"user_login_time"`
	UserLoginIP       int32  `gorm:"column:user_login_ip;type:int(10) unsigned;not null" json:"user_login_ip"`
	UserLastLoginTime int32  `gorm:"column:user_last_login_time;type:int(10) unsigned;not null" json:"user_last_login_time"`
	UserLastLoginIP   int32  `gorm:"column:user_last_login_ip;type:int(10) unsigned;not null" json:"user_last_login_ip"`
	UserLoginNum      int32  `gorm:"column:user_login_num;type:smallint(6) unsigned;not null" json:"user_login_num"`
	UserExtend        int32  `gorm:"column:user_extend;type:smallint(6) unsigned;not null" json:"user_extend"`
	UserRandom        string `gorm:"column:user_random;type:varchar(32);not null" json:"user_random"`
	UserEndTime       int32  `gorm:"column:user_end_time;type:int(10) unsigned;not null" json:"user_end_time"`
	UserPid           int32  `gorm:"column:user_pid;type:int(10) unsigned;not null" json:"user_pid"`
	UserPid2          int32  `gorm:"column:user_pid_2;type:int(10) unsigned;not null" json:"user_pid_2"`
	UserPid3          int32  `gorm:"column:user_pid_3;type:int(10) unsigned;not null" json:"user_pid_3"`
}

// TableName ThoUser's table name
func (*ThoUser) TableName() string {
	return TableNameThoUser
}
