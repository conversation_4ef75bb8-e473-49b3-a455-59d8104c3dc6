// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameThoCard = "tho_card"

// ThoCard mapped from table <tho_card>
type ThoCard struct {
	CardID         int32  `gorm:"column:card_id;type:int(10) unsigned;primaryKey;autoIncrement:true" json:"card_id"`
	CardNo         string `gorm:"column:card_no;type:varchar(16);not null;index:card_no,priority:1" json:"card_no"`
	CardPwd        string `gorm:"column:card_pwd;type:varchar(8);not null;index:card_pwd,priority:1" json:"card_pwd"`
	CardMoney      int32  `gorm:"column:card_money;type:smallint(6) unsigned;not null" json:"card_money"`
	CardPoints     int32  `gorm:"column:card_points;type:smallint(6) unsigned;not null" json:"card_points"`
	CardUseStatus  int8   `gorm:"column:card_use_status;type:tinyint(1) unsigned;not null" json:"card_use_status"`
	CardSaleStatus int8   `gorm:"column:card_sale_status;type:tinyint(1) unsigned;not null" json:"card_sale_status"`
	UserID         int32  `gorm:"column:user_id;type:int(10) unsigned;not null;index:user_id,priority:1" json:"user_id"`
	CardAddTime    int32  `gorm:"column:card_add_time;type:int(10) unsigned;not null;index:card_add_time,priority:1" json:"card_add_time"`
	CardUseTime    int32  `gorm:"column:card_use_time;type:int(10) unsigned;not null;index:card_use_time,priority:1" json:"card_use_time"`
}

// TableName ThoCard's table name
func (*ThoCard) TableName() string {
	return TableNameThoCard
}
