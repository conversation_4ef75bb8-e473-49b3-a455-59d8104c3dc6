// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameThoCollect = "tho_collect"

// ThoCollect mapped from table <tho_collect>
type ThoCollect struct {
	CollectID         int32  `gorm:"column:collect_id;type:int(10) unsigned;primaryKey;autoIncrement:true" json:"collect_id"`
	CollectName       string `gorm:"column:collect_name;type:varchar(30);not null" json:"collect_name"`
	CollectURL        string `gorm:"column:collect_url;type:varchar(255);not null" json:"collect_url"`
	CollectType       int8   `gorm:"column:collect_type;type:tinyint(1) unsigned;not null;default:1" json:"collect_type"`
	CollectMid        int8   `gorm:"column:collect_mid;type:tinyint(1) unsigned;not null;default:1" json:"collect_mid"`
	CollectAppid      string `gorm:"column:collect_appid;type:varchar(30);not null" json:"collect_appid"`
	CollectAppkey     string `gorm:"column:collect_appkey;type:varchar(30);not null" json:"collect_appkey"`
	CollectParam      string `gorm:"column:collect_param;type:varchar(100);not null" json:"collect_param"`
	CollectFilter     int8   `gorm:"column:collect_filter;type:tinyint(1) unsigned;not null" json:"collect_filter"`
	CollectFilterFrom string `gorm:"column:collect_filter_from;type:varchar(255);not null" json:"collect_filter_from"`
	CollectFilterYear string `gorm:"column:collect_filter_year;type:varchar(255);not null;comment:采集时，过滤年份" json:"collect_filter_year"` // 采集时，过滤年份
	CollectOpt        int8   `gorm:"column:collect_opt;type:tinyint(1) unsigned;not null" json:"collect_opt"`
	CollectSyncPicOpt int8   `gorm:"column:collect_sync_pic_opt;type:tinyint(1) unsigned;not null;comment:同步图片选项，0-跟随全局，1-开启，2-关闭" json:"collect_sync_pic_opt"` // 同步图片选项，0-跟随全局，1-开启，2-关闭
}

// TableName ThoCollect's table name
func (*ThoCollect) TableName() string {
	return TableNameThoCollect
}
