// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameThoVisit = "tho_visit"

// ThoVisit mapped from table <tho_visit>
type ThoVisit struct {
	VisitID   int32  `gorm:"column:visit_id;type:int(10) unsigned;primaryKey;autoIncrement:true" json:"visit_id"`
	UserID    *int32 `gorm:"column:user_id;type:int(10) unsigned;index:user_id,priority:1" json:"user_id"`
	VisitIP   int32  `gorm:"column:visit_ip;type:int(10) unsigned;not null" json:"visit_ip"`
	VisitLy   string `gorm:"column:visit_ly;type:varchar(100);not null" json:"visit_ly"`
	VisitTime int32  `gorm:"column:visit_time;type:int(10) unsigned;not null;index:visit_time,priority:1" json:"visit_time"`
}

// TableName ThoVisit's table name
func (*ThoVisit) TableName() string {
	return TableNameThoVisit
}
