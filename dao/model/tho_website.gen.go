// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameThoWebsite = "tho_website"

// ThoWebsite mapped from table <tho_website>
type ThoWebsite struct {
	WebsiteID            int32   `gorm:"column:website_id;type:int(10) unsigned;primaryKey;autoIncrement:true" json:"website_id"`
	TypeID               int32   `gorm:"column:type_id;type:smallint(5) unsigned;not null;index:type_id,priority:1" json:"type_id"`
	TypeId1              int32   `gorm:"column:type_id_1;type:smallint(5) unsigned;not null;index:type_id_1,priority:1" json:"type_id_1"`
	WebsiteName          string  `gorm:"column:website_name;type:varchar(60);not null;index:website_name,priority:1" json:"website_name"`
	WebsiteSub           string  `gorm:"column:website_sub;type:varchar(255);not null" json:"website_sub"`
	WebsiteEn            string  `gorm:"column:website_en;type:varchar(255);not null;index:website_en,priority:1" json:"website_en"`
	WebsiteStatus        int8    `gorm:"column:website_status;type:tinyint(1) unsigned;not null" json:"website_status"`
	WebsiteLetter        string  `gorm:"column:website_letter;type:char(1);not null;index:website_letter,priority:1" json:"website_letter"`
	WebsiteColor         string  `gorm:"column:website_color;type:varchar(6);not null" json:"website_color"`
	WebsiteLock          int8    `gorm:"column:website_lock;type:tinyint(1) unsigned;not null;index:website_lock,priority:1" json:"website_lock"`
	WebsiteSort          int32   `gorm:"column:website_sort;type:int(10);not null;index:website_sort,priority:1" json:"website_sort"`
	WebsiteJumpurl       string  `gorm:"column:website_jumpurl;type:varchar(255);not null" json:"website_jumpurl"`
	WebsitePic           string  `gorm:"column:website_pic;type:varchar(1024);not null" json:"website_pic"`
	WebsitePicScreenshot *string `gorm:"column:website_pic_screenshot;type:text" json:"website_pic_screenshot"`
	WebsiteLogo          string  `gorm:"column:website_logo;type:varchar(255);not null" json:"website_logo"`
	WebsiteArea          string  `gorm:"column:website_area;type:varchar(20);not null" json:"website_area"`
	WebsiteLang          string  `gorm:"column:website_lang;type:varchar(10);not null" json:"website_lang"`
	WebsiteLevel         int8    `gorm:"column:website_level;type:tinyint(1) unsigned;not null;index:website_level,priority:1" json:"website_level"`
	WebsiteTime          int32   `gorm:"column:website_time;type:int(10) unsigned;not null;index:website_time,priority:1" json:"website_time"`
	WebsiteTimeAdd       int32   `gorm:"column:website_time_add;type:int(10) unsigned;not null;index:website_time_add,priority:1" json:"website_time_add"`
	WebsiteTimeHits      int32   `gorm:"column:website_time_hits;type:int(10) unsigned;not null" json:"website_time_hits"`
	WebsiteTimeMake      int32   `gorm:"column:website_time_make;type:int(10) unsigned;not null;index:website_time_make,priority:1" json:"website_time_make"`
	WebsiteTimeReferer   int32   `gorm:"column:website_time_referer;type:int(10) unsigned;not null;index:website_time_referer,priority:1" json:"website_time_referer"`
	WebsiteHits          int32   `gorm:"column:website_hits;type:mediumint(8) unsigned;not null;index:website_hits,priority:1" json:"website_hits"`
	WebsiteHitsDay       int32   `gorm:"column:website_hits_day;type:mediumint(8) unsigned;not null;index:website_hits_day,priority:1" json:"website_hits_day"`
	WebsiteHitsWeek      int32   `gorm:"column:website_hits_week;type:mediumint(8) unsigned;not null;index:website_hits_week,priority:1" json:"website_hits_week"`
	WebsiteHitsMonth     int32   `gorm:"column:website_hits_month;type:mediumint(8) unsigned;not null;index:website_hits_month,priority:1" json:"website_hits_month"`
	WebsiteScore         float64 `gorm:"column:website_score;type:decimal(3,1) unsigned;not null;index:website_score,priority:1;default:0.0" json:"website_score"`
	WebsiteScoreAll      int32   `gorm:"column:website_score_all;type:mediumint(8) unsigned;not null;index:website_score_all,priority:1" json:"website_score_all"`
	WebsiteScoreNum      int32   `gorm:"column:website_score_num;type:mediumint(8) unsigned;not null;index:website_score_num,priority:1" json:"website_score_num"`
	WebsiteUp            int32   `gorm:"column:website_up;type:mediumint(8) unsigned;not null;index:website_up,priority:1" json:"website_up"`
	WebsiteDown          int32   `gorm:"column:website_down;type:mediumint(8) unsigned;not null;index:website_down,priority:1" json:"website_down"`
	WebsiteReferer       int32   `gorm:"column:website_referer;type:mediumint(8) unsigned;not null;index:website_referer,priority:1" json:"website_referer"`
	WebsiteRefererDay    int32   `gorm:"column:website_referer_day;type:mediumint(8) unsigned;not null;index:website_referer_day,priority:1" json:"website_referer_day"`
	WebsiteRefererWeek   int32   `gorm:"column:website_referer_week;type:mediumint(8) unsigned;not null;index:website_referer_week,priority:1" json:"website_referer_week"`
	WebsiteRefererMonth  int32   `gorm:"column:website_referer_month;type:mediumint(8) unsigned;not null;index:website_referer_month,priority:1" json:"website_referer_month"`
	WebsiteTag           string  `gorm:"column:website_tag;type:varchar(100);not null;index:website_tag,priority:1" json:"website_tag"`
	WebsiteClass         string  `gorm:"column:website_class;type:varchar(255);not null;index:website_class,priority:1" json:"website_class"`
	WebsiteRemarks       string  `gorm:"column:website_remarks;type:varchar(100);not null" json:"website_remarks"`
	WebsiteTpl           string  `gorm:"column:website_tpl;type:varchar(30);not null" json:"website_tpl"`
	WebsiteBlurb         string  `gorm:"column:website_blurb;type:varchar(255);not null" json:"website_blurb"`
	WebsiteContent       string  `gorm:"column:website_content;type:mediumtext;not null" json:"website_content"`
}

// TableName ThoWebsite's table name
func (*ThoWebsite) TableName() string {
	return TableNameThoWebsite
}
