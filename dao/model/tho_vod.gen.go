// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameThoVod = "tho_vod"

// ThoVod mapped from table <tho_vod>
type ThoVod struct {
	VodID            int32   `gorm:"column:vod_id;type:int(10) unsigned;primaryKey;autoIncrement:true" json:"vod_id"`
	TypeID           int32   `gorm:"column:type_id;type:smallint(6);not null;index:type_id,priority:1" json:"type_id"`
	TypeId1          int32   `gorm:"column:type_id_1;type:smallint(6) unsigned;not null;index:type_id_1,priority:1" json:"type_id_1"`
	GroupID          int32   `gorm:"column:group_id;type:smallint(6) unsigned;not null;index:group_id,priority:1" json:"group_id"`
	VodName          string  `gorm:"column:vod_name;type:varchar(255);not null;index:vod_name,priority:1" json:"vod_name"`
	VodSub           string  `gorm:"column:vod_sub;type:varchar(255);not null" json:"vod_sub"`
	VodEn            string  `gorm:"column:vod_en;type:varchar(255);not null;index:vod_en,priority:1" json:"vod_en"`
	VodStatus        int8    `gorm:"column:vod_status;type:tinyint(1) unsigned;not null" json:"vod_status"`
	VodLetter        string  `gorm:"column:vod_letter;type:char(1);not null;index:vod_letter,priority:1" json:"vod_letter"`
	VodColor         string  `gorm:"column:vod_color;type:varchar(6);not null" json:"vod_color"`
	VodTag           string  `gorm:"column:vod_tag;type:varchar(100);not null;index:vod_tag,priority:1" json:"vod_tag"`
	VodClass         string  `gorm:"column:vod_class;type:varchar(255);not null;index:vod_class,priority:1" json:"vod_class"`
	VodPic           string  `gorm:"column:vod_pic;type:varchar(1024);not null" json:"vod_pic"`
	VodPicThumb      string  `gorm:"column:vod_pic_thumb;type:varchar(1024);not null" json:"vod_pic_thumb"`
	VodPicSlide      string  `gorm:"column:vod_pic_slide;type:varchar(1024);not null" json:"vod_pic_slide"`
	VodPicScreenshot *string `gorm:"column:vod_pic_screenshot;type:text" json:"vod_pic_screenshot"`
	VodActor         string  `gorm:"column:vod_actor;type:varchar(255);not null;index:vod_actor,priority:1" json:"vod_actor"`
	VodDirector      string  `gorm:"column:vod_director;type:varchar(255);not null;index:vod_director,priority:1" json:"vod_director"`
	VodWriter        string  `gorm:"column:vod_writer;type:varchar(100);not null" json:"vod_writer"`
	VodBehind        string  `gorm:"column:vod_behind;type:varchar(100);not null" json:"vod_behind"`
	VodBlurb         string  `gorm:"column:vod_blurb;type:varchar(255);not null" json:"vod_blurb"`
	VodRemarks       string  `gorm:"column:vod_remarks;type:varchar(100);not null" json:"vod_remarks"`
	VodPubdate       string  `gorm:"column:vod_pubdate;type:varchar(100);not null" json:"vod_pubdate"`
	VodTotal         int32   `gorm:"column:vod_total;type:mediumint(8) unsigned;not null;index:vod_total,priority:1" json:"vod_total"`
	VodSerial        string  `gorm:"column:vod_serial;type:varchar(20);not null;default:0" json:"vod_serial"`
	VodTv            string  `gorm:"column:vod_tv;type:varchar(30);not null" json:"vod_tv"`
	VodWeekday       string  `gorm:"column:vod_weekday;type:varchar(30);not null" json:"vod_weekday"`
	VodArea          string  `gorm:"column:vod_area;type:varchar(20);not null;index:vod_area,priority:1" json:"vod_area"`
	VodLang          string  `gorm:"column:vod_lang;type:varchar(10);not null;index:vod_lang,priority:1" json:"vod_lang"`
	VodYear          string  `gorm:"column:vod_year;type:varchar(10);not null;index:vod_year,priority:1" json:"vod_year"`
	VodVersion       string  `gorm:"column:vod_version;type:varchar(30);not null;index:vod_version,priority:1" json:"vod_version"`
	VodState         string  `gorm:"column:vod_state;type:varchar(30);not null;index:vod_state,priority:1" json:"vod_state"`
	VodAuthor        string  `gorm:"column:vod_author;type:varchar(60);not null" json:"vod_author"`
	VodJumpurl       string  `gorm:"column:vod_jumpurl;type:varchar(150);not null" json:"vod_jumpurl"`
	VodTpl           string  `gorm:"column:vod_tpl;type:varchar(30);not null" json:"vod_tpl"`
	VodTplPlay       string  `gorm:"column:vod_tpl_play;type:varchar(30);not null" json:"vod_tpl_play"`
	VodTplDown       string  `gorm:"column:vod_tpl_down;type:varchar(30);not null" json:"vod_tpl_down"`
	VodIsend         int8    `gorm:"column:vod_isend;type:tinyint(1) unsigned;not null;index:vod_isend,priority:1" json:"vod_isend"`
	VodLock          int8    `gorm:"column:vod_lock;type:tinyint(1) unsigned;not null;index:vod_lock,priority:1" json:"vod_lock"`
	VodLevel         int8    `gorm:"column:vod_level;type:tinyint(1) unsigned;not null;index:vod_level,priority:1" json:"vod_level"`
	VodCopyright     int8    `gorm:"column:vod_copyright;type:tinyint(1) unsigned;not null" json:"vod_copyright"`
	VodPoints        int32   `gorm:"column:vod_points;type:smallint(6) unsigned;not null" json:"vod_points"`
	VodPointsPlay    int32   `gorm:"column:vod_points_play;type:smallint(6) unsigned;not null;index:vod_points_play,priority:1" json:"vod_points_play"`
	VodPointsDown    int32   `gorm:"column:vod_points_down;type:smallint(6) unsigned;not null;index:vod_points_down,priority:1" json:"vod_points_down"`
	VodHits          int32   `gorm:"column:vod_hits;type:mediumint(8) unsigned;not null;index:vod_hits,priority:1" json:"vod_hits"`
	VodHitsDay       int32   `gorm:"column:vod_hits_day;type:mediumint(8) unsigned;not null;index:vod_hits_day,priority:1" json:"vod_hits_day"`
	VodHitsWeek      int32   `gorm:"column:vod_hits_week;type:mediumint(8) unsigned;not null;index:vod_hits_week,priority:1" json:"vod_hits_week"`
	VodHitsMonth     int32   `gorm:"column:vod_hits_month;type:mediumint(8) unsigned;not null;index:vod_hits_month,priority:1" json:"vod_hits_month"`
	VodDuration      string  `gorm:"column:vod_duration;type:varchar(10);not null" json:"vod_duration"`
	VodUp            int32   `gorm:"column:vod_up;type:mediumint(8) unsigned;not null;index:vod_up,priority:1" json:"vod_up"`
	VodDown          int32   `gorm:"column:vod_down;type:mediumint(8) unsigned;not null;index:vod_down,priority:1" json:"vod_down"`
	VodScore         float64 `gorm:"column:vod_score;type:decimal(3,1) unsigned;not null;index:vod_score,priority:1;default:0.0" json:"vod_score"`
	VodScoreAll      int32   `gorm:"column:vod_score_all;type:mediumint(8) unsigned;not null;index:vod_score_all,priority:1" json:"vod_score_all"`
	VodScoreNum      int32   `gorm:"column:vod_score_num;type:mediumint(8) unsigned;not null;index:vod_score_num,priority:1" json:"vod_score_num"`
	VodTime          int32   `gorm:"column:vod_time;type:int(10) unsigned;not null;index:vod_time,priority:1" json:"vod_time"`
	VodTimeAdd       int32   `gorm:"column:vod_time_add;type:int(10) unsigned;not null;index:vod_time_add,priority:1" json:"vod_time_add"`
	VodTimeHits      int32   `gorm:"column:vod_time_hits;type:int(10) unsigned;not null" json:"vod_time_hits"`
	VodTimeMake      int32   `gorm:"column:vod_time_make;type:int(10) unsigned;not null;index:vod_time_make,priority:1" json:"vod_time_make"`
	VodTrysee        int32   `gorm:"column:vod_trysee;type:smallint(6) unsigned;not null" json:"vod_trysee"`
	VodDoubanID      int32   `gorm:"column:vod_douban_id;type:int(10) unsigned;not null" json:"vod_douban_id"`
	VodDoubanScore   float64 `gorm:"column:vod_douban_score;type:decimal(3,1) unsigned;not null;default:0.0" json:"vod_douban_score"`
	VodReurl         string  `gorm:"column:vod_reurl;type:varchar(255);not null" json:"vod_reurl"`
	VodRelVod        string  `gorm:"column:vod_rel_vod;type:varchar(255);not null" json:"vod_rel_vod"`
	VodRelArt        string  `gorm:"column:vod_rel_art;type:varchar(255);not null" json:"vod_rel_art"`
	VodPwd           string  `gorm:"column:vod_pwd;type:varchar(10);not null" json:"vod_pwd"`
	VodPwdURL        string  `gorm:"column:vod_pwd_url;type:varchar(255);not null" json:"vod_pwd_url"`
	VodPwdPlay       string  `gorm:"column:vod_pwd_play;type:varchar(10);not null" json:"vod_pwd_play"`
	VodPwdPlayURL    string  `gorm:"column:vod_pwd_play_url;type:varchar(255);not null" json:"vod_pwd_play_url"`
	VodPwdDown       string  `gorm:"column:vod_pwd_down;type:varchar(10);not null" json:"vod_pwd_down"`
	VodPwdDownURL    string  `gorm:"column:vod_pwd_down_url;type:varchar(255);not null" json:"vod_pwd_down_url"`
	VodContent       string  `gorm:"column:vod_content;type:mediumtext;not null" json:"vod_content"`
	VodPlayFrom      string  `gorm:"column:vod_play_from;type:varchar(255);not null" json:"vod_play_from"`
	VodPlayServer    string  `gorm:"column:vod_play_server;type:varchar(255);not null" json:"vod_play_server"`
	VodPlayNote      string  `gorm:"column:vod_play_note;type:varchar(255);not null" json:"vod_play_note"`
	VodPlayURL       string  `gorm:"column:vod_play_url;type:mediumtext;not null" json:"vod_play_url"`
	VodDownFrom      string  `gorm:"column:vod_down_from;type:varchar(255);not null" json:"vod_down_from"`
	VodDownServer    string  `gorm:"column:vod_down_server;type:varchar(255);not null" json:"vod_down_server"`
	VodDownNote      string  `gorm:"column:vod_down_note;type:varchar(255);not null" json:"vod_down_note"`
	VodDownURL       string  `gorm:"column:vod_down_url;type:mediumtext;not null" json:"vod_down_url"`
	VodPlot          int8    `gorm:"column:vod_plot;type:tinyint(1) unsigned;not null;index:vod_plot,priority:1" json:"vod_plot"`
	VodPlotName      string  `gorm:"column:vod_plot_name;type:mediumtext;not null" json:"vod_plot_name"`
	VodPlotDetail    string  `gorm:"column:vod_plot_detail;type:mediumtext;not null" json:"vod_plot_detail"`
}

// TableName ThoVod's table name
func (*ThoVod) TableName() string {
	return TableNameThoVod
}
