// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameThoCjNode = "tho_cj_node"

// ThoCjNode mapped from table <tho_cj_node>
type ThoCjNode struct {
	Nodeid           int32  `gorm:"column:nodeid;type:smallint(6) unsigned;primaryKey;autoIncrement:true" json:"nodeid"`
	Name             string `gorm:"column:name;type:varchar(20);not null" json:"name"`
	Lastdate         int32  `gorm:"column:lastdate;type:int(10) unsigned;not null" json:"lastdate"`
	Sourcecharset    string `gorm:"column:sourcecharset;type:varchar(8);not null" json:"sourcecharset"`
	Sourcetype       int8   `gorm:"column:sourcetype;type:tinyint(1) unsigned;not null" json:"sourcetype"`
	Urlpage          string `gorm:"column:urlpage;type:text;not null" json:"urlpage"`
	PagesizeStart    int8   `gorm:"column:pagesize_start;type:tinyint(3) unsigned;not null" json:"pagesize_start"`
	PagesizeEnd      int32  `gorm:"column:pagesize_end;type:mediumint(8) unsigned;not null" json:"pagesize_end"`
	PageBase         string `gorm:"column:page_base;type:char(255);not null" json:"page_base"`
	ParNum           int8   `gorm:"column:par_num;type:tinyint(3) unsigned;not null;default:1" json:"par_num"`
	URLContain       string `gorm:"column:url_contain;type:char(100);not null" json:"url_contain"`
	URLExcept        string `gorm:"column:url_except;type:char(100);not null" json:"url_except"`
	URLStart         string `gorm:"column:url_start;type:char(100);not null" json:"url_start"`
	URLEnd           string `gorm:"column:url_end;type:char(100);not null" json:"url_end"`
	TitleRule        string `gorm:"column:title_rule;type:char(100);not null" json:"title_rule"`
	TitleHTMLRule    string `gorm:"column:title_html_rule;type:text;not null" json:"title_html_rule"`
	TypeRule         string `gorm:"column:type_rule;type:char(100);not null" json:"type_rule"`
	TypeHTMLRule     string `gorm:"column:type_html_rule;type:text;not null" json:"type_html_rule"`
	ContentRule      string `gorm:"column:content_rule;type:char(100);not null" json:"content_rule"`
	ContentHTMLRule  string `gorm:"column:content_html_rule;type:text;not null" json:"content_html_rule"`
	ContentPageStart string `gorm:"column:content_page_start;type:char(100);not null" json:"content_page_start"`
	ContentPageEnd   string `gorm:"column:content_page_end;type:char(100);not null" json:"content_page_end"`
	ContentPageRule  int8   `gorm:"column:content_page_rule;type:tinyint(1) unsigned;not null" json:"content_page_rule"`
	ContentPage      int8   `gorm:"column:content_page;type:tinyint(1) unsigned;not null" json:"content_page"`
	ContentNextpage  string `gorm:"column:content_nextpage;type:char(100);not null" json:"content_nextpage"`
	DownAttachment   int8   `gorm:"column:down_attachment;type:tinyint(1) unsigned;not null" json:"down_attachment"`
	Watermark        int8   `gorm:"column:watermark;type:tinyint(1) unsigned;not null" json:"watermark"`
	CollOrder        int8   `gorm:"column:coll_order;type:tinyint(3) unsigned;not null" json:"coll_order"`
	CustomizeConfig  string `gorm:"column:customize_config;type:text;not null" json:"customize_config"`
	ProgramConfig    string `gorm:"column:program_config;type:text;not null" json:"program_config"`
	Mid              int8   `gorm:"column:mid;type:tinyint(1) unsigned;not null;default:1" json:"mid"`
}

// TableName ThoCjNode's table name
func (*ThoCjNode) TableName() string {
	return TableNameThoCjNode
}
