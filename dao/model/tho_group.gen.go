// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameThoGroup = "tho_group"

// ThoGroup mapped from table <tho_group>
type ThoGroup struct {
	GroupID          int32  `gorm:"column:group_id;type:smallint(6);primaryKey;autoIncrement:true" json:"group_id"`
	GroupName        string `gorm:"column:group_name;type:varchar(30);not null" json:"group_name"`
	GroupStatus      int8   `gorm:"column:group_status;type:tinyint(1) unsigned;not null;index:group_status,priority:1;default:1" json:"group_status"`
	GroupType        string `gorm:"column:group_type;type:text;not null" json:"group_type"`
	GroupPopedom     string `gorm:"column:group_popedom;type:text;not null" json:"group_popedom"`
	GroupPointsDay   int32  `gorm:"column:group_points_day;type:smallint(6) unsigned;not null" json:"group_points_day"`
	GroupPointsWeek  int32  `gorm:"column:group_points_week;type:smallint(6);not null" json:"group_points_week"`
	GroupPointsMonth int32  `gorm:"column:group_points_month;type:smallint(6) unsigned;not null" json:"group_points_month"`
	GroupPointsYear  int32  `gorm:"column:group_points_year;type:smallint(6) unsigned;not null" json:"group_points_year"`
	GroupPointsFree  int8   `gorm:"column:group_points_free;type:tinyint(1) unsigned;not null" json:"group_points_free"`
}

// TableName ThoGroup's table name
func (*ThoGroup) TableName() string {
	return TableNameThoGroup
}
