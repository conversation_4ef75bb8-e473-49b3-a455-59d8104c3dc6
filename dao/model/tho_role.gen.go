// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameThoRole = "tho_role"

// ThoRole mapped from table <tho_role>
type ThoRole struct {
	RoleID        int32   `gorm:"column:role_id;type:int(10) unsigned;primaryKey;autoIncrement:true" json:"role_id"`
	RoleRid       int32   `gorm:"column:role_rid;type:int(10) unsigned;not null;index:role_rid,priority:1" json:"role_rid"`
	RoleName      string  `gorm:"column:role_name;type:varchar(255);not null;index:role_name,priority:1" json:"role_name"`
	RoleEn        string  `gorm:"column:role_en;type:varchar(255);not null;index:role_en,priority:1" json:"role_en"`
	RoleStatus    int8    `gorm:"column:role_status;type:tinyint(1) unsigned;not null" json:"role_status"`
	RoleLock      int8    `gorm:"column:role_lock;type:tinyint(1) unsigned;not null" json:"role_lock"`
	RoleLetter    string  `gorm:"column:role_letter;type:char(1);not null;index:role_letter,priority:1" json:"role_letter"`
	RoleColor     string  `gorm:"column:role_color;type:varchar(6);not null" json:"role_color"`
	RoleActor     string  `gorm:"column:role_actor;type:varchar(255);not null;index:role_actor,priority:1" json:"role_actor"`
	RoleRemarks   string  `gorm:"column:role_remarks;type:varchar(100);not null" json:"role_remarks"`
	RolePic       string  `gorm:"column:role_pic;type:varchar(1024);not null" json:"role_pic"`
	RoleSort      int32   `gorm:"column:role_sort;type:smallint(6) unsigned;not null" json:"role_sort"`
	RoleLevel     int8    `gorm:"column:role_level;type:tinyint(1) unsigned;not null;index:role_level,priority:1" json:"role_level"`
	RoleTime      int32   `gorm:"column:role_time;type:int(10) unsigned;not null;index:role_time,priority:1" json:"role_time"`
	RoleTimeAdd   int32   `gorm:"column:role_time_add;type:int(10) unsigned;not null;index:role_time_add,priority:1" json:"role_time_add"`
	RoleTimeHits  int32   `gorm:"column:role_time_hits;type:int(10) unsigned;not null" json:"role_time_hits"`
	RoleTimeMake  int32   `gorm:"column:role_time_make;type:int(10) unsigned;not null" json:"role_time_make"`
	RoleHits      int32   `gorm:"column:role_hits;type:mediumint(8) unsigned;not null" json:"role_hits"`
	RoleHitsDay   int32   `gorm:"column:role_hits_day;type:mediumint(8) unsigned;not null" json:"role_hits_day"`
	RoleHitsWeek  int32   `gorm:"column:role_hits_week;type:mediumint(8) unsigned;not null" json:"role_hits_week"`
	RoleHitsMonth int32   `gorm:"column:role_hits_month;type:mediumint(8) unsigned;not null" json:"role_hits_month"`
	RoleScore     float64 `gorm:"column:role_score;type:decimal(3,1) unsigned;not null;index:role_score,priority:1;default:0.0" json:"role_score"`
	RoleScoreAll  int32   `gorm:"column:role_score_all;type:mediumint(8) unsigned;not null;index:role_score_all,priority:1" json:"role_score_all"`
	RoleScoreNum  int32   `gorm:"column:role_score_num;type:mediumint(8) unsigned;not null;index:role_score_num,priority:1" json:"role_score_num"`
	RoleUp        int32   `gorm:"column:role_up;type:mediumint(8) unsigned;not null;index:role_up,priority:1" json:"role_up"`
	RoleDown      int32   `gorm:"column:role_down;type:mediumint(8) unsigned;not null;index:role_down,priority:1" json:"role_down"`
	RoleTpl       string  `gorm:"column:role_tpl;type:varchar(30);not null" json:"role_tpl"`
	RoleJumpurl   string  `gorm:"column:role_jumpurl;type:varchar(150);not null" json:"role_jumpurl"`
	RoleContent   string  `gorm:"column:role_content;type:text;not null" json:"role_content"`
}

// TableName ThoRole's table name
func (*ThoRole) TableName() string {
	return TableNameThoRole
}
