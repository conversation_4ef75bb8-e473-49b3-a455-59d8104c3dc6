// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameThoPlog = "tho_plog"

// ThoPlog mapped from table <tho_plog>
type ThoPlog struct {
	PlogID      int32  `gorm:"column:plog_id;type:int(10) unsigned;primaryKey;autoIncrement:true" json:"plog_id"`
	UserID      int32  `gorm:"column:user_id;type:int(10) unsigned;not null;index:user_id,priority:1" json:"user_id"`
	UserId1     int32  `gorm:"column:user_id_1;type:int(10);not null" json:"user_id_1"`
	PlogType    int8   `gorm:"column:plog_type;type:tinyint(1) unsigned;not null;index:plog_type,priority:1;default:1" json:"plog_type"`
	PlogPoints  int32  `gorm:"column:plog_points;type:smallint(6) unsigned;not null" json:"plog_points"`
	PlogTime    int32  `gorm:"column:plog_time;type:int(10) unsigned;not null" json:"plog_time"`
	PlogRemarks string `gorm:"column:plog_remarks;type:varchar(100);not null" json:"plog_remarks"`
}

// TableName ThoPlog's table name
func (*ThoPlog) TableName() string {
	return TableNameThoPlog
}
