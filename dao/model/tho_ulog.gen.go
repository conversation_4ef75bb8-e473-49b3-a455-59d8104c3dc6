// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameThoUlog = "tho_ulog"

// ThoUlog mapped from table <tho_ulog>
type ThoUlog struct {
	UlogID     int32 `gorm:"column:ulog_id;type:int(10) unsigned;primaryKey;autoIncrement:true" json:"ulog_id"`
	UserID     int32 `gorm:"column:user_id;type:int(10) unsigned;not null;index:user_id,priority:1" json:"user_id"`
	UlogMid    int8  `gorm:"column:ulog_mid;type:tinyint(1) unsigned;not null;index:ulog_mid,priority:1" json:"ulog_mid"`
	UlogType   int8  `gorm:"column:ulog_type;type:tinyint(1) unsigned;not null;index:ulog_type,priority:1;default:1" json:"ulog_type"`
	UlogRid    int32 `gorm:"column:ulog_rid;type:int(10) unsigned;not null;index:ulog_rid,priority:1" json:"ulog_rid"`
	UlogSid    int8  `gorm:"column:ulog_sid;type:tinyint(3) unsigned;not null" json:"ulog_sid"`
	UlogNid    int32 `gorm:"column:ulog_nid;type:smallint(6) unsigned;not null" json:"ulog_nid"`
	UlogPoints int32 `gorm:"column:ulog_points;type:smallint(6) unsigned;not null" json:"ulog_points"`
	UlogTime   int32 `gorm:"column:ulog_time;type:int(10) unsigned;not null" json:"ulog_time"`
}

// TableName ThoUlog's table name
func (*ThoUlog) TableName() string {
	return TableNameThoUlog
}
