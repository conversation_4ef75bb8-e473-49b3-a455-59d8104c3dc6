// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameThoActor = "tho_actor"

// ThoActor mapped from table <tho_actor>
type ThoActor struct {
	ActorID        int32   `gorm:"column:actor_id;type:int(10) unsigned;primaryKey;autoIncrement:true" json:"actor_id"`
	TypeID         int32   `gorm:"column:type_id;type:smallint(6) unsigned;not null;index:type_id,priority:1" json:"type_id"`
	TypeId1        int32   `gorm:"column:type_id_1;type:smallint(6) unsigned;not null;index:type_id_1,priority:1" json:"type_id_1"`
	ActorName      string  `gorm:"column:actor_name;type:varchar(255);not null;index:actor_name,priority:1" json:"actor_name"`
	ActorEn        string  `gorm:"column:actor_en;type:varchar(255);not null;index:actor_en,priority:1" json:"actor_en"`
	ActorAlias     string  `gorm:"column:actor_alias;type:varchar(255);not null" json:"actor_alias"`
	ActorStatus    int8    `gorm:"column:actor_status;type:tinyint(1) unsigned;not null" json:"actor_status"`
	ActorLock      int8    `gorm:"column:actor_lock;type:tinyint(1) unsigned;not null" json:"actor_lock"`
	ActorLetter    string  `gorm:"column:actor_letter;type:char(1);not null;index:actor_letter,priority:1" json:"actor_letter"`
	ActorSex       string  `gorm:"column:actor_sex;type:char(1);not null;index:actor_sex,priority:1" json:"actor_sex"`
	ActorColor     string  `gorm:"column:actor_color;type:varchar(6);not null" json:"actor_color"`
	ActorPic       string  `gorm:"column:actor_pic;type:varchar(1024);not null" json:"actor_pic"`
	ActorBlurb     string  `gorm:"column:actor_blurb;type:varchar(255);not null" json:"actor_blurb"`
	ActorRemarks   string  `gorm:"column:actor_remarks;type:varchar(100);not null" json:"actor_remarks"`
	ActorArea      string  `gorm:"column:actor_area;type:varchar(20);not null;index:actor_area,priority:1" json:"actor_area"`
	ActorHeight    string  `gorm:"column:actor_height;type:varchar(10);not null" json:"actor_height"`
	ActorWeight    string  `gorm:"column:actor_weight;type:varchar(10);not null" json:"actor_weight"`
	ActorBirthday  string  `gorm:"column:actor_birthday;type:varchar(10);not null" json:"actor_birthday"`
	ActorBirtharea string  `gorm:"column:actor_birtharea;type:varchar(20);not null" json:"actor_birtharea"`
	ActorBlood     string  `gorm:"column:actor_blood;type:varchar(10);not null" json:"actor_blood"`
	ActorStarsign  string  `gorm:"column:actor_starsign;type:varchar(10);not null" json:"actor_starsign"`
	ActorSchool    string  `gorm:"column:actor_school;type:varchar(20);not null" json:"actor_school"`
	ActorWorks     string  `gorm:"column:actor_works;type:varchar(255);not null" json:"actor_works"`
	ActorTag       string  `gorm:"column:actor_tag;type:varchar(255);not null;index:actor_tag,priority:1" json:"actor_tag"`
	ActorClass     string  `gorm:"column:actor_class;type:varchar(255);not null;index:actor_class,priority:1" json:"actor_class"`
	ActorLevel     int8    `gorm:"column:actor_level;type:tinyint(1) unsigned;not null;index:actor_level,priority:1" json:"actor_level"`
	ActorTime      int32   `gorm:"column:actor_time;type:int(10) unsigned;not null;index:actor_time,priority:1" json:"actor_time"`
	ActorTimeAdd   int32   `gorm:"column:actor_time_add;type:int(10) unsigned;not null;index:actor_time_add,priority:1" json:"actor_time_add"`
	ActorTimeHits  int32   `gorm:"column:actor_time_hits;type:int(10) unsigned;not null" json:"actor_time_hits"`
	ActorTimeMake  int32   `gorm:"column:actor_time_make;type:int(10) unsigned;not null" json:"actor_time_make"`
	ActorHits      int32   `gorm:"column:actor_hits;type:mediumint(8) unsigned;not null" json:"actor_hits"`
	ActorHitsDay   int32   `gorm:"column:actor_hits_day;type:mediumint(8) unsigned;not null" json:"actor_hits_day"`
	ActorHitsWeek  int32   `gorm:"column:actor_hits_week;type:mediumint(8) unsigned;not null" json:"actor_hits_week"`
	ActorHitsMonth int32   `gorm:"column:actor_hits_month;type:mediumint(8) unsigned;not null" json:"actor_hits_month"`
	ActorScore     float64 `gorm:"column:actor_score;type:decimal(3,1) unsigned;not null;index:actor_score,priority:1;default:0.0" json:"actor_score"`
	ActorScoreAll  int32   `gorm:"column:actor_score_all;type:mediumint(8) unsigned;not null;index:actor_score_all,priority:1" json:"actor_score_all"`
	ActorScoreNum  int32   `gorm:"column:actor_score_num;type:mediumint(8) unsigned;not null;index:actor_score_num,priority:1" json:"actor_score_num"`
	ActorUp        int32   `gorm:"column:actor_up;type:mediumint(8) unsigned;not null;index:actor_up,priority:1" json:"actor_up"`
	ActorDown      int32   `gorm:"column:actor_down;type:mediumint(8) unsigned;not null;index:actor_down,priority:1" json:"actor_down"`
	ActorTpl       string  `gorm:"column:actor_tpl;type:varchar(30);not null" json:"actor_tpl"`
	ActorJumpurl   string  `gorm:"column:actor_jumpurl;type:varchar(150);not null" json:"actor_jumpurl"`
	ActorContent   string  `gorm:"column:actor_content;type:text;not null" json:"actor_content"`
}

// TableName ThoActor's table name
func (*ThoActor) TableName() string {
	return TableNameThoActor
}
