// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameThoComment = "tho_comment"

// ThoComment mapped from table <tho_comment>
type ThoComment struct {
	CommentID      int32  `gorm:"column:comment_id;type:int(10) unsigned;primaryKey;autoIncrement:true" json:"comment_id"`
	CommentMid     int8   `gorm:"column:comment_mid;type:tinyint(1) unsigned;not null;index:comment_mid,priority:1;default:1" json:"comment_mid"`
	CommentRid     int32  `gorm:"column:comment_rid;type:int(10) unsigned;not null;index:comment_rid,priority:1" json:"comment_rid"`
	CommentPid     int32  `gorm:"column:comment_pid;type:int(10) unsigned;not null;index:comment_pid,priority:1" json:"comment_pid"`
	UserID         int32  `gorm:"column:user_id;type:int(10) unsigned;not null;index:user_id,priority:1" json:"user_id"`
	CommentStatus  int8   `gorm:"column:comment_status;type:tinyint(1) unsigned;not null;default:1" json:"comment_status"`
	CommentName    string `gorm:"column:comment_name;type:varchar(60);not null" json:"comment_name"`
	CommentIP      int32  `gorm:"column:comment_ip;type:int(10) unsigned;not null" json:"comment_ip"`
	CommentTime    int32  `gorm:"column:comment_time;type:int(10) unsigned;not null;index:comment_time,priority:1" json:"comment_time"`
	CommentContent string `gorm:"column:comment_content;type:varchar(255);not null" json:"comment_content"`
	CommentUp      int32  `gorm:"column:comment_up;type:mediumint(8) unsigned;not null" json:"comment_up"`
	CommentDown    int32  `gorm:"column:comment_down;type:mediumint(8) unsigned;not null" json:"comment_down"`
	CommentReply   int32  `gorm:"column:comment_reply;type:mediumint(8) unsigned;not null;index:comment_reply,priority:1" json:"comment_reply"`
	CommentReport  int32  `gorm:"column:comment_report;type:mediumint(8) unsigned;not null" json:"comment_report"`
}

// TableName ThoComment's table name
func (*ThoComment) TableName() string {
	return TableNameThoComment
}
