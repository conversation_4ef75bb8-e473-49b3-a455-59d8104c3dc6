// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameThoCash = "tho_cash"

// ThoCash mapped from table <tho_cash>
type ThoCash struct {
	CashID        int32   `gorm:"column:cash_id;type:int(10) unsigned;primaryKey;autoIncrement:true" json:"cash_id"`
	UserID        int32   `gorm:"column:user_id;type:int(10) unsigned;not null;index:user_id,priority:1" json:"user_id"`
	CashStatus    int8    `gorm:"column:cash_status;type:tinyint(1) unsigned;not null;index:cash_status,priority:1" json:"cash_status"`
	CashPoints    int32   `gorm:"column:cash_points;type:smallint(6) unsigned;not null" json:"cash_points"`
	CashMoney     float64 `gorm:"column:cash_money;type:decimal(12,2) unsigned;not null;default:0.00" json:"cash_money"`
	CashBankName  string  `gorm:"column:cash_bank_name;type:varchar(60);not null" json:"cash_bank_name"`
	CashBankNo    string  `gorm:"column:cash_bank_no;type:varchar(30);not null" json:"cash_bank_no"`
	CashPayeeName string  `gorm:"column:cash_payee_name;type:varchar(30);not null" json:"cash_payee_name"`
	CashTime      int32   `gorm:"column:cash_time;type:int(10) unsigned;not null" json:"cash_time"`
	CashTimeAudit int32   `gorm:"column:cash_time_audit;type:int(10) unsigned;not null" json:"cash_time_audit"`
}

// TableName ThoCash's table name
func (*ThoCash) TableName() string {
	return TableNameThoCash
}
