// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameThoVodSearch = "tho_vod_search"

// ThoVodSearch mapped from table <tho_vod_search>
type ThoVodSearch struct {
	SearchKey         string `gorm:"column:search_key;type:char(32);primaryKey;comment:搜索键（关键词md5）" json:"search_key"`                                                            // 搜索键（关键词md5）
	SearchWord        string `gorm:"column:search_word;type:varchar(128);not null;comment:搜索关键词" json:"search_word"`                                                              // 搜索关键词
	SearchField       string `gorm:"column:search_field;type:varchar(64);not null;index:search_field,priority:1;comment:搜索字段名（可有多个，用|分隔）" json:"search_field"`                    // 搜索字段名（可有多个，用|分隔）
	SearchHitCount    int64  `gorm:"column:search_hit_count;type:bigint(20) unsigned;not null;index:search_hit_count,priority:1;comment:搜索命中次数" json:"search_hit_count"`          // 搜索命中次数
	SearchLastHitTime int32  `gorm:"column:search_last_hit_time;type:int(10) unsigned;not null;index:search_last_hit_time,priority:1;comment:最近命中时间" json:"search_last_hit_time"` // 最近命中时间
	SearchUpdateTime  int32  `gorm:"column:search_update_time;type:int(10) unsigned;not null;index:search_update_time,priority:1;comment:添加时间" json:"search_update_time"`         // 添加时间
	SearchResultCount int32  `gorm:"column:search_result_count;type:int(10) unsigned;not null;comment:结果Id数量" json:"search_result_count"`                                         // 结果Id数量
	SearchResultIds   string `gorm:"column:search_result_ids;type:mediumtext;not null;comment:搜索结果Id列表，英文半角逗号分隔" json:"search_result_ids"`                                        // 搜索结果Id列表，英文半角逗号分隔
}

// TableName ThoVodSearch's table name
func (*ThoVodSearch) TableName() string {
	return TableNameThoVodSearch
}
