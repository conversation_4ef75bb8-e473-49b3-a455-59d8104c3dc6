// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameThoGbook = "tho_gbook"

// ThoGbook mapped from table <tho_gbook>
type ThoGbook struct {
	GbookID        int32  `gorm:"column:gbook_id;type:int(10) unsigned;primaryKey;autoIncrement:true" json:"gbook_id"`
	GbookRid       int32  `gorm:"column:gbook_rid;type:int(10) unsigned;not null;index:gbook_rid,priority:1" json:"gbook_rid"`
	UserID         int32  `gorm:"column:user_id;type:int(10) unsigned;not null;index:user_id,priority:1" json:"user_id"`
	GbookStatus    int8   `gorm:"column:gbook_status;type:tinyint(1) unsigned;not null;default:1" json:"gbook_status"`
	GbookName      string `gorm:"column:gbook_name;type:varchar(60);not null" json:"gbook_name"`
	GbookIP        int32  `gorm:"column:gbook_ip;type:int(10) unsigned;not null" json:"gbook_ip"`
	GbookTime      int32  `gorm:"column:gbook_time;type:int(10) unsigned;not null;index:gbook_time,priority:1" json:"gbook_time"`
	GbookReplyTime int32  `gorm:"column:gbook_reply_time;type:int(10) unsigned;not null;index:gbook_reply_time,priority:1" json:"gbook_reply_time"`
	GbookContent   string `gorm:"column:gbook_content;type:varchar(255);not null" json:"gbook_content"`
	GbookReply     string `gorm:"column:gbook_reply;type:varchar(255);not null;index:gbook_reply,priority:1" json:"gbook_reply"`
}

// TableName ThoGbook's table name
func (*ThoGbook) TableName() string {
	return TableNameThoGbook
}
