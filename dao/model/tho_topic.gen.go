// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameThoTopic = "tho_topic"

// ThoTopic mapped from table <tho_topic>
type ThoTopic struct {
	TopicID        int32   `gorm:"column:topic_id;type:smallint(6) unsigned;primaryKey;autoIncrement:true" json:"topic_id"`
	TopicName      string  `gorm:"column:topic_name;type:varchar(255);not null;index:topic_name,priority:1" json:"topic_name"`
	TopicEn        string  `gorm:"column:topic_en;type:varchar(255);not null;index:topic_en,priority:1" json:"topic_en"`
	TopicSub       string  `gorm:"column:topic_sub;type:varchar(255);not null" json:"topic_sub"`
	TopicStatus    int8    `gorm:"column:topic_status;type:tinyint(1) unsigned;not null;default:1" json:"topic_status"`
	TopicSort      int32   `gorm:"column:topic_sort;type:smallint(6) unsigned;not null;index:topic_sort,priority:1" json:"topic_sort"`
	TopicLetter    string  `gorm:"column:topic_letter;type:char(1);not null" json:"topic_letter"`
	TopicColor     string  `gorm:"column:topic_color;type:varchar(6);not null" json:"topic_color"`
	TopicTpl       string  `gorm:"column:topic_tpl;type:varchar(30);not null" json:"topic_tpl"`
	TopicType      string  `gorm:"column:topic_type;type:varchar(255);not null" json:"topic_type"`
	TopicPic       string  `gorm:"column:topic_pic;type:varchar(1024);not null" json:"topic_pic"`
	TopicPicThumb  string  `gorm:"column:topic_pic_thumb;type:varchar(1024);not null" json:"topic_pic_thumb"`
	TopicPicSlide  string  `gorm:"column:topic_pic_slide;type:varchar(1024);not null" json:"topic_pic_slide"`
	TopicKey       string  `gorm:"column:topic_key;type:varchar(255);not null" json:"topic_key"`
	TopicDes       string  `gorm:"column:topic_des;type:varchar(255);not null" json:"topic_des"`
	TopicTitle     string  `gorm:"column:topic_title;type:varchar(255);not null" json:"topic_title"`
	TopicBlurb     string  `gorm:"column:topic_blurb;type:varchar(255);not null" json:"topic_blurb"`
	TopicRemarks   string  `gorm:"column:topic_remarks;type:varchar(100);not null" json:"topic_remarks"`
	TopicLevel     int8    `gorm:"column:topic_level;type:tinyint(1) unsigned;not null;index:topic_level,priority:1" json:"topic_level"`
	TopicUp        int32   `gorm:"column:topic_up;type:mediumint(8) unsigned;not null;index:topic_up,priority:1" json:"topic_up"`
	TopicDown      int32   `gorm:"column:topic_down;type:mediumint(8) unsigned;not null;index:topic_down,priority:1" json:"topic_down"`
	TopicScore     float64 `gorm:"column:topic_score;type:decimal(3,1) unsigned;not null;index:topic_score,priority:1;default:0.0" json:"topic_score"`
	TopicScoreAll  int32   `gorm:"column:topic_score_all;type:mediumint(8) unsigned;not null;index:topic_score_all,priority:1" json:"topic_score_all"`
	TopicScoreNum  int32   `gorm:"column:topic_score_num;type:mediumint(8) unsigned;not null;index:topic_score_num,priority:1" json:"topic_score_num"`
	TopicHits      int32   `gorm:"column:topic_hits;type:mediumint(8) unsigned;not null;index:topic_hits,priority:1" json:"topic_hits"`
	TopicHitsDay   int32   `gorm:"column:topic_hits_day;type:mediumint(8) unsigned;not null;index:topic_hits_day,priority:1" json:"topic_hits_day"`
	TopicHitsWeek  int32   `gorm:"column:topic_hits_week;type:mediumint(8) unsigned;not null;index:topic_hits_week,priority:1" json:"topic_hits_week"`
	TopicHitsMonth int32   `gorm:"column:topic_hits_month;type:mediumint(8) unsigned;not null;index:topic_hits_month,priority:1" json:"topic_hits_month"`
	TopicTime      int32   `gorm:"column:topic_time;type:int(10) unsigned;not null;index:topic_time,priority:1" json:"topic_time"`
	TopicTimeAdd   int32   `gorm:"column:topic_time_add;type:int(10) unsigned;not null;index:topic_time_add,priority:1" json:"topic_time_add"`
	TopicTimeHits  int32   `gorm:"column:topic_time_hits;type:int(10) unsigned;not null;index:topic_time_hits,priority:1" json:"topic_time_hits"`
	TopicTimeMake  int32   `gorm:"column:topic_time_make;type:int(10) unsigned;not null" json:"topic_time_make"`
	TopicTag       string  `gorm:"column:topic_tag;type:varchar(255);not null" json:"topic_tag"`
	TopicRelVod    string  `gorm:"column:topic_rel_vod;type:text;not null" json:"topic_rel_vod"`
	TopicRelArt    string  `gorm:"column:topic_rel_art;type:text;not null" json:"topic_rel_art"`
	TopicContent   string  `gorm:"column:topic_content;type:text;not null" json:"topic_content"`
	TopicExtend    string  `gorm:"column:topic_extend;type:text;not null" json:"topic_extend"`
}

// TableName ThoTopic's table name
func (*ThoTopic) TableName() string {
	return TableNameThoTopic
}
