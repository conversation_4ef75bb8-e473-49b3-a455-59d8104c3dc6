// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameThoArt = "tho_art"

// ThoArt mapped from table <tho_art>
type ThoArt struct {
	ArtID            int32   `gorm:"column:art_id;type:int(10) unsigned;primaryKey;autoIncrement:true" json:"art_id"`
	TypeID           int32   `gorm:"column:type_id;type:smallint(6) unsigned;not null;index:type_id,priority:1" json:"type_id"`
	TypeId1          int32   `gorm:"column:type_id_1;type:smallint(6) unsigned;not null;index:type_id_1,priority:1" json:"type_id_1"`
	GroupID          int32   `gorm:"column:group_id;type:smallint(6) unsigned;not null" json:"group_id"`
	ArtName          string  `gorm:"column:art_name;type:varchar(255);not null;index:art_name,priority:1" json:"art_name"`
	ArtSub           string  `gorm:"column:art_sub;type:varchar(255);not null" json:"art_sub"`
	ArtEn            string  `gorm:"column:art_en;type:varchar(255);not null;index:art_enn,priority:1" json:"art_en"`
	ArtStatus        int8    `gorm:"column:art_status;type:tinyint(1) unsigned;not null" json:"art_status"`
	ArtLetter        string  `gorm:"column:art_letter;type:char(1);not null;index:art_letter,priority:1" json:"art_letter"`
	ArtColor         string  `gorm:"column:art_color;type:varchar(6);not null" json:"art_color"`
	ArtFrom          string  `gorm:"column:art_from;type:varchar(30);not null" json:"art_from"`
	ArtAuthor        string  `gorm:"column:art_author;type:varchar(30);not null" json:"art_author"`
	ArtTag           string  `gorm:"column:art_tag;type:varchar(100);not null;index:art_tag,priority:1" json:"art_tag"`
	ArtClass         string  `gorm:"column:art_class;type:varchar(255);not null" json:"art_class"`
	ArtPic           string  `gorm:"column:art_pic;type:varchar(1024);not null" json:"art_pic"`
	ArtPicThumb      string  `gorm:"column:art_pic_thumb;type:varchar(1024);not null" json:"art_pic_thumb"`
	ArtPicSlide      string  `gorm:"column:art_pic_slide;type:varchar(1024);not null" json:"art_pic_slide"`
	ArtPicScreenshot *string `gorm:"column:art_pic_screenshot;type:text" json:"art_pic_screenshot"`
	ArtBlurb         string  `gorm:"column:art_blurb;type:varchar(255);not null" json:"art_blurb"`
	ArtRemarks       string  `gorm:"column:art_remarks;type:varchar(100);not null" json:"art_remarks"`
	ArtJumpurl       string  `gorm:"column:art_jumpurl;type:varchar(150);not null" json:"art_jumpurl"`
	ArtTpl           string  `gorm:"column:art_tpl;type:varchar(30);not null" json:"art_tpl"`
	ArtLevel         int8    `gorm:"column:art_level;type:tinyint(1) unsigned;not null;index:art_level,priority:1" json:"art_level"`
	ArtLock          int8    `gorm:"column:art_lock;type:tinyint(1) unsigned;not null;index:art_lock,priority:1" json:"art_lock"`
	ArtPoints        int32   `gorm:"column:art_points;type:smallint(6) unsigned;not null" json:"art_points"`
	ArtPointsDetail  int32   `gorm:"column:art_points_detail;type:smallint(6) unsigned;not null" json:"art_points_detail"`
	ArtUp            int32   `gorm:"column:art_up;type:mediumint(8) unsigned;not null;index:art_up,priority:1" json:"art_up"`
	ArtDown          int32   `gorm:"column:art_down;type:mediumint(8) unsigned;not null;index:art_down,priority:1" json:"art_down"`
	ArtHits          int32   `gorm:"column:art_hits;type:mediumint(8) unsigned;not null;index:art_hits,priority:1" json:"art_hits"`
	ArtHitsDay       int32   `gorm:"column:art_hits_day;type:mediumint(8) unsigned;not null;index:art_hits_day,priority:1" json:"art_hits_day"`
	ArtHitsWeek      int32   `gorm:"column:art_hits_week;type:mediumint(8) unsigned;not null;index:art_hits_week,priority:1" json:"art_hits_week"`
	ArtHitsMonth     int32   `gorm:"column:art_hits_month;type:mediumint(8) unsigned;not null;index:art_hits_month,priority:1" json:"art_hits_month"`
	ArtTime          int32   `gorm:"column:art_time;type:int(10) unsigned;not null;index:art_time,priority:1" json:"art_time"`
	ArtTimeAdd       int32   `gorm:"column:art_time_add;type:int(10) unsigned;not null;index:art_time_add,priority:1" json:"art_time_add"`
	ArtTimeHits      int32   `gorm:"column:art_time_hits;type:int(10) unsigned;not null" json:"art_time_hits"`
	ArtTimeMake      int32   `gorm:"column:art_time_make;type:int(10) unsigned;not null;index:art_time_make,priority:1" json:"art_time_make"`
	ArtScore         float64 `gorm:"column:art_score;type:decimal(3,1) unsigned;not null;index:art_score,priority:1;default:0.0" json:"art_score"`
	ArtScoreAll      int32   `gorm:"column:art_score_all;type:mediumint(8) unsigned;not null;index:art_score_all,priority:1" json:"art_score_all"`
	ArtScoreNum      int32   `gorm:"column:art_score_num;type:mediumint(8) unsigned;not null;index:art_score_num,priority:1" json:"art_score_num"`
	ArtRelArt        string  `gorm:"column:art_rel_art;type:varchar(255);not null" json:"art_rel_art"`
	ArtRelVod        string  `gorm:"column:art_rel_vod;type:varchar(255);not null" json:"art_rel_vod"`
	ArtPwd           string  `gorm:"column:art_pwd;type:varchar(10);not null" json:"art_pwd"`
	ArtPwdURL        string  `gorm:"column:art_pwd_url;type:varchar(255);not null" json:"art_pwd_url"`
	ArtTitle         string  `gorm:"column:art_title;type:mediumtext;not null" json:"art_title"`
	ArtNote          string  `gorm:"column:art_note;type:mediumtext;not null" json:"art_note"`
	ArtContent       string  `gorm:"column:art_content;type:mediumtext;not null" json:"art_content"`
}

// TableName ThoArt's table name
func (*ThoArt) TableName() string {
	return TableNameThoArt
}
