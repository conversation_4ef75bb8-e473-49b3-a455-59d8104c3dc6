// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameThoOrder = "tho_order"

// ThoOrder mapped from table <tho_order>
type ThoOrder struct {
	OrderID      int32   `gorm:"column:order_id;type:int(10) unsigned;primaryKey;autoIncrement:true" json:"order_id"`
	UserID       int32   `gorm:"column:user_id;type:int(10) unsigned;not null;index:user_id,priority:1" json:"user_id"`
	OrderStatus  int8    `gorm:"column:order_status;type:tinyint(1) unsigned;not null" json:"order_status"`
	OrderCode    string  `gorm:"column:order_code;type:varchar(30);not null;index:order_code,priority:1" json:"order_code"`
	OrderPrice   float64 `gorm:"column:order_price;type:decimal(12,2) unsigned;not null;default:0.00" json:"order_price"`
	OrderTime    int32   `gorm:"column:order_time;type:int(10) unsigned;not null;index:order_time,priority:1" json:"order_time"`
	OrderPoints  int32   `gorm:"column:order_points;type:mediumint(8) unsigned;not null" json:"order_points"`
	OrderPayType string  `gorm:"column:order_pay_type;type:varchar(10);not null" json:"order_pay_type"`
	OrderPayTime int32   `gorm:"column:order_pay_time;type:int(10) unsigned;not null" json:"order_pay_time"`
	OrderRemarks string  `gorm:"column:order_remarks;type:varchar(100);not null" json:"order_remarks"`
}

// TableName ThoOrder's table name
func (*ThoOrder) TableName() string {
	return TableNameThoOrder
}
