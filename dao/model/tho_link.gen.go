// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameThoLink = "tho_link"

// ThoLink mapped from table <tho_link>
type ThoLink struct {
	LinkID      int32  `gorm:"column:link_id;type:smallint(6) unsigned;primaryKey;autoIncrement:true" json:"link_id"`
	LinkType    int8   `gorm:"column:link_type;type:tinyint(1) unsigned;not null;index:link_type,priority:1" json:"link_type"`
	LinkName    string `gorm:"column:link_name;type:varchar(60);not null" json:"link_name"`
	LinkSort    int32  `gorm:"column:link_sort;type:smallint(6);not null;index:link_sort,priority:1" json:"link_sort"`
	LinkAddTime int32  `gorm:"column:link_add_time;type:int(10) unsigned;not null;index:link_add_time,priority:1" json:"link_add_time"`
	LinkTime    int32  `gorm:"column:link_time;type:int(10) unsigned;not null;index:link_time,priority:1" json:"link_time"`
	LinkURL     string `gorm:"column:link_url;type:varchar(255);not null" json:"link_url"`
	LinkLogo    string `gorm:"column:link_logo;type:varchar(255);not null" json:"link_logo"`
}

// TableName ThoLink's table name
func (*ThoLink) TableName() string {
	return TableNameThoLink
}
