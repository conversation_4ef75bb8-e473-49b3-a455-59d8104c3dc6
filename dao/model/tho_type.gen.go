// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package model

const TableNameThoType = "tho_type"

// ThoType mapped from table <tho_type>
type ThoType struct {
	TypeID        int32  `gorm:"column:type_id;type:smallint(6) unsigned;primaryKey;autoIncrement:true" json:"type_id"`
	TypeName      string `gorm:"column:type_name;type:varchar(60);not null;index:type_name,priority:1" json:"type_name"`
	TypeEn        string `gorm:"column:type_en;type:varchar(60);not null;index:type_en,priority:1" json:"type_en"`
	TypeSort      int32  `gorm:"column:type_sort;type:smallint(6) unsigned;not null;index:type_sort,priority:1" json:"type_sort"`
	TypeMid       int32  `gorm:"column:type_mid;type:smallint(6) unsigned;not null;index:type_mid,priority:1;default:1" json:"type_mid"`
	TypePid       int32  `gorm:"column:type_pid;type:smallint(6) unsigned;not null;index:type_pid,priority:1" json:"type_pid"`
	TypeStatus    int8   `gorm:"column:type_status;type:tinyint(1) unsigned;not null;default:1" json:"type_status"`
	TypeTpl       string `gorm:"column:type_tpl;type:varchar(30);not null" json:"type_tpl"`
	TypeTplList   string `gorm:"column:type_tpl_list;type:varchar(30);not null" json:"type_tpl_list"`
	TypeTplDetail string `gorm:"column:type_tpl_detail;type:varchar(30);not null" json:"type_tpl_detail"`
	TypeTplPlay   string `gorm:"column:type_tpl_play;type:varchar(30);not null" json:"type_tpl_play"`
	TypeTplDown   string `gorm:"column:type_tpl_down;type:varchar(30);not null" json:"type_tpl_down"`
	TypeKey       string `gorm:"column:type_key;type:varchar(255);not null" json:"type_key"`
	TypeDes       string `gorm:"column:type_des;type:varchar(255);not null" json:"type_des"`
	TypeTitle     string `gorm:"column:type_title;type:varchar(255);not null" json:"type_title"`
	TypeUnion     string `gorm:"column:type_union;type:varchar(255);not null" json:"type_union"`
	TypeExtend    string `gorm:"column:type_extend;type:text;not null" json:"type_extend"`
	TypeLogo      string `gorm:"column:type_logo;type:varchar(255);not null" json:"type_logo"`
	TypePic       string `gorm:"column:type_pic;type:varchar(1024);not null" json:"type_pic"`
	TypeJumpurl   string `gorm:"column:type_jumpurl;type:varchar(150);not null" json:"type_jumpurl"`
}

// TableName ThoType's table name
func (*ThoType) TableName() string {
	return TableNameThoType
}
