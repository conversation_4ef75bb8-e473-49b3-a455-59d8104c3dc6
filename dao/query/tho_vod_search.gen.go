// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/wilsonce/rabbitmv_bot/dao/model"
)

func newThoVodSearch(db *gorm.DB, opts ...gen.DOOption) thoVodSearch {
	_thoVodSearch := thoVodSearch{}

	_thoVodSearch.thoVodSearchDo.UseDB(db, opts...)
	_thoVodSearch.thoVodSearchDo.UseModel(&model.ThoVodSearch{})

	tableName := _thoVodSearch.thoVodSearchDo.TableName()
	_thoVodSearch.ALL = field.NewAsterisk(tableName)
	_thoVodSearch.SearchKey = field.NewString(tableName, "search_key")
	_thoVodSearch.SearchWord = field.NewString(tableName, "search_word")
	_thoVodSearch.SearchField = field.NewString(tableName, "search_field")
	_thoVodSearch.SearchHitCount = field.NewInt64(tableName, "search_hit_count")
	_thoVodSearch.SearchLastHitTime = field.NewInt32(tableName, "search_last_hit_time")
	_thoVodSearch.SearchUpdateTime = field.NewInt32(tableName, "search_update_time")
	_thoVodSearch.SearchResultCount = field.NewInt32(tableName, "search_result_count")
	_thoVodSearch.SearchResultIds = field.NewString(tableName, "search_result_ids")

	_thoVodSearch.fillFieldMap()

	return _thoVodSearch
}

type thoVodSearch struct {
	thoVodSearchDo

	ALL               field.Asterisk
	SearchKey         field.String // 搜索键（关键词md5）
	SearchWord        field.String // 搜索关键词
	SearchField       field.String // 搜索字段名（可有多个，用|分隔）
	SearchHitCount    field.Int64  // 搜索命中次数
	SearchLastHitTime field.Int32  // 最近命中时间
	SearchUpdateTime  field.Int32  // 添加时间
	SearchResultCount field.Int32  // 结果Id数量
	SearchResultIds   field.String // 搜索结果Id列表，英文半角逗号分隔

	fieldMap map[string]field.Expr
}

func (t thoVodSearch) Table(newTableName string) *thoVodSearch {
	t.thoVodSearchDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thoVodSearch) As(alias string) *thoVodSearch {
	t.thoVodSearchDo.DO = *(t.thoVodSearchDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thoVodSearch) updateTableName(table string) *thoVodSearch {
	t.ALL = field.NewAsterisk(table)
	t.SearchKey = field.NewString(table, "search_key")
	t.SearchWord = field.NewString(table, "search_word")
	t.SearchField = field.NewString(table, "search_field")
	t.SearchHitCount = field.NewInt64(table, "search_hit_count")
	t.SearchLastHitTime = field.NewInt32(table, "search_last_hit_time")
	t.SearchUpdateTime = field.NewInt32(table, "search_update_time")
	t.SearchResultCount = field.NewInt32(table, "search_result_count")
	t.SearchResultIds = field.NewString(table, "search_result_ids")

	t.fillFieldMap()

	return t
}

func (t *thoVodSearch) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thoVodSearch) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 8)
	t.fieldMap["search_key"] = t.SearchKey
	t.fieldMap["search_word"] = t.SearchWord
	t.fieldMap["search_field"] = t.SearchField
	t.fieldMap["search_hit_count"] = t.SearchHitCount
	t.fieldMap["search_last_hit_time"] = t.SearchLastHitTime
	t.fieldMap["search_update_time"] = t.SearchUpdateTime
	t.fieldMap["search_result_count"] = t.SearchResultCount
	t.fieldMap["search_result_ids"] = t.SearchResultIds
}

func (t thoVodSearch) clone(db *gorm.DB) thoVodSearch {
	t.thoVodSearchDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thoVodSearch) replaceDB(db *gorm.DB) thoVodSearch {
	t.thoVodSearchDo.ReplaceDB(db)
	return t
}

type thoVodSearchDo struct{ gen.DO }

type IThoVodSearchDo interface {
	gen.SubQuery
	Debug() IThoVodSearchDo
	WithContext(ctx context.Context) IThoVodSearchDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThoVodSearchDo
	WriteDB() IThoVodSearchDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThoVodSearchDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThoVodSearchDo
	Not(conds ...gen.Condition) IThoVodSearchDo
	Or(conds ...gen.Condition) IThoVodSearchDo
	Select(conds ...field.Expr) IThoVodSearchDo
	Where(conds ...gen.Condition) IThoVodSearchDo
	Order(conds ...field.Expr) IThoVodSearchDo
	Distinct(cols ...field.Expr) IThoVodSearchDo
	Omit(cols ...field.Expr) IThoVodSearchDo
	Join(table schema.Tabler, on ...field.Expr) IThoVodSearchDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThoVodSearchDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThoVodSearchDo
	Group(cols ...field.Expr) IThoVodSearchDo
	Having(conds ...gen.Condition) IThoVodSearchDo
	Limit(limit int) IThoVodSearchDo
	Offset(offset int) IThoVodSearchDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThoVodSearchDo
	Unscoped() IThoVodSearchDo
	Create(values ...*model.ThoVodSearch) error
	CreateInBatches(values []*model.ThoVodSearch, batchSize int) error
	Save(values ...*model.ThoVodSearch) error
	First() (*model.ThoVodSearch, error)
	Take() (*model.ThoVodSearch, error)
	Last() (*model.ThoVodSearch, error)
	Find() ([]*model.ThoVodSearch, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoVodSearch, err error)
	FindInBatches(result *[]*model.ThoVodSearch, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThoVodSearch) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThoVodSearchDo
	Assign(attrs ...field.AssignExpr) IThoVodSearchDo
	Joins(fields ...field.RelationField) IThoVodSearchDo
	Preload(fields ...field.RelationField) IThoVodSearchDo
	FirstOrInit() (*model.ThoVodSearch, error)
	FirstOrCreate() (*model.ThoVodSearch, error)
	FindByPage(offset int, limit int) (result []*model.ThoVodSearch, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThoVodSearchDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thoVodSearchDo) Debug() IThoVodSearchDo {
	return t.withDO(t.DO.Debug())
}

func (t thoVodSearchDo) WithContext(ctx context.Context) IThoVodSearchDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thoVodSearchDo) ReadDB() IThoVodSearchDo {
	return t.Clauses(dbresolver.Read)
}

func (t thoVodSearchDo) WriteDB() IThoVodSearchDo {
	return t.Clauses(dbresolver.Write)
}

func (t thoVodSearchDo) Session(config *gorm.Session) IThoVodSearchDo {
	return t.withDO(t.DO.Session(config))
}

func (t thoVodSearchDo) Clauses(conds ...clause.Expression) IThoVodSearchDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thoVodSearchDo) Returning(value interface{}, columns ...string) IThoVodSearchDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thoVodSearchDo) Not(conds ...gen.Condition) IThoVodSearchDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thoVodSearchDo) Or(conds ...gen.Condition) IThoVodSearchDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thoVodSearchDo) Select(conds ...field.Expr) IThoVodSearchDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thoVodSearchDo) Where(conds ...gen.Condition) IThoVodSearchDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thoVodSearchDo) Order(conds ...field.Expr) IThoVodSearchDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thoVodSearchDo) Distinct(cols ...field.Expr) IThoVodSearchDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thoVodSearchDo) Omit(cols ...field.Expr) IThoVodSearchDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thoVodSearchDo) Join(table schema.Tabler, on ...field.Expr) IThoVodSearchDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thoVodSearchDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThoVodSearchDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thoVodSearchDo) RightJoin(table schema.Tabler, on ...field.Expr) IThoVodSearchDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thoVodSearchDo) Group(cols ...field.Expr) IThoVodSearchDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thoVodSearchDo) Having(conds ...gen.Condition) IThoVodSearchDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thoVodSearchDo) Limit(limit int) IThoVodSearchDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thoVodSearchDo) Offset(offset int) IThoVodSearchDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thoVodSearchDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThoVodSearchDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thoVodSearchDo) Unscoped() IThoVodSearchDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thoVodSearchDo) Create(values ...*model.ThoVodSearch) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thoVodSearchDo) CreateInBatches(values []*model.ThoVodSearch, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thoVodSearchDo) Save(values ...*model.ThoVodSearch) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thoVodSearchDo) First() (*model.ThoVodSearch, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoVodSearch), nil
	}
}

func (t thoVodSearchDo) Take() (*model.ThoVodSearch, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoVodSearch), nil
	}
}

func (t thoVodSearchDo) Last() (*model.ThoVodSearch, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoVodSearch), nil
	}
}

func (t thoVodSearchDo) Find() ([]*model.ThoVodSearch, error) {
	result, err := t.DO.Find()
	return result.([]*model.ThoVodSearch), err
}

func (t thoVodSearchDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoVodSearch, err error) {
	buf := make([]*model.ThoVodSearch, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thoVodSearchDo) FindInBatches(result *[]*model.ThoVodSearch, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thoVodSearchDo) Attrs(attrs ...field.AssignExpr) IThoVodSearchDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thoVodSearchDo) Assign(attrs ...field.AssignExpr) IThoVodSearchDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thoVodSearchDo) Joins(fields ...field.RelationField) IThoVodSearchDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thoVodSearchDo) Preload(fields ...field.RelationField) IThoVodSearchDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thoVodSearchDo) FirstOrInit() (*model.ThoVodSearch, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoVodSearch), nil
	}
}

func (t thoVodSearchDo) FirstOrCreate() (*model.ThoVodSearch, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoVodSearch), nil
	}
}

func (t thoVodSearchDo) FindByPage(offset int, limit int) (result []*model.ThoVodSearch, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thoVodSearchDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thoVodSearchDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thoVodSearchDo) Delete(models ...*model.ThoVodSearch) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thoVodSearchDo) withDO(do gen.Dao) *thoVodSearchDo {
	t.DO = *do.(*gen.DO)
	return t
}
