// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/wilsonce/rabbitmv_bot/dao/model"
)

func newThoGroup(db *gorm.DB, opts ...gen.DOOption) thoGroup {
	_thoGroup := thoGroup{}

	_thoGroup.thoGroupDo.UseDB(db, opts...)
	_thoGroup.thoGroupDo.UseModel(&model.ThoGroup{})

	tableName := _thoGroup.thoGroupDo.TableName()
	_thoGroup.ALL = field.NewAsterisk(tableName)
	_thoGroup.GroupID = field.NewInt32(tableName, "group_id")
	_thoGroup.GroupName = field.NewString(tableName, "group_name")
	_thoGroup.GroupStatus = field.NewInt8(tableName, "group_status")
	_thoGroup.GroupType = field.NewString(tableName, "group_type")
	_thoGroup.GroupPopedom = field.NewString(tableName, "group_popedom")
	_thoGroup.GroupPointsDay = field.NewInt32(tableName, "group_points_day")
	_thoGroup.GroupPointsWeek = field.NewInt32(tableName, "group_points_week")
	_thoGroup.GroupPointsMonth = field.NewInt32(tableName, "group_points_month")
	_thoGroup.GroupPointsYear = field.NewInt32(tableName, "group_points_year")
	_thoGroup.GroupPointsFree = field.NewInt8(tableName, "group_points_free")

	_thoGroup.fillFieldMap()

	return _thoGroup
}

type thoGroup struct {
	thoGroupDo

	ALL              field.Asterisk
	GroupID          field.Int32
	GroupName        field.String
	GroupStatus      field.Int8
	GroupType        field.String
	GroupPopedom     field.String
	GroupPointsDay   field.Int32
	GroupPointsWeek  field.Int32
	GroupPointsMonth field.Int32
	GroupPointsYear  field.Int32
	GroupPointsFree  field.Int8

	fieldMap map[string]field.Expr
}

func (t thoGroup) Table(newTableName string) *thoGroup {
	t.thoGroupDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thoGroup) As(alias string) *thoGroup {
	t.thoGroupDo.DO = *(t.thoGroupDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thoGroup) updateTableName(table string) *thoGroup {
	t.ALL = field.NewAsterisk(table)
	t.GroupID = field.NewInt32(table, "group_id")
	t.GroupName = field.NewString(table, "group_name")
	t.GroupStatus = field.NewInt8(table, "group_status")
	t.GroupType = field.NewString(table, "group_type")
	t.GroupPopedom = field.NewString(table, "group_popedom")
	t.GroupPointsDay = field.NewInt32(table, "group_points_day")
	t.GroupPointsWeek = field.NewInt32(table, "group_points_week")
	t.GroupPointsMonth = field.NewInt32(table, "group_points_month")
	t.GroupPointsYear = field.NewInt32(table, "group_points_year")
	t.GroupPointsFree = field.NewInt8(table, "group_points_free")

	t.fillFieldMap()

	return t
}

func (t *thoGroup) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thoGroup) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 10)
	t.fieldMap["group_id"] = t.GroupID
	t.fieldMap["group_name"] = t.GroupName
	t.fieldMap["group_status"] = t.GroupStatus
	t.fieldMap["group_type"] = t.GroupType
	t.fieldMap["group_popedom"] = t.GroupPopedom
	t.fieldMap["group_points_day"] = t.GroupPointsDay
	t.fieldMap["group_points_week"] = t.GroupPointsWeek
	t.fieldMap["group_points_month"] = t.GroupPointsMonth
	t.fieldMap["group_points_year"] = t.GroupPointsYear
	t.fieldMap["group_points_free"] = t.GroupPointsFree
}

func (t thoGroup) clone(db *gorm.DB) thoGroup {
	t.thoGroupDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thoGroup) replaceDB(db *gorm.DB) thoGroup {
	t.thoGroupDo.ReplaceDB(db)
	return t
}

type thoGroupDo struct{ gen.DO }

type IThoGroupDo interface {
	gen.SubQuery
	Debug() IThoGroupDo
	WithContext(ctx context.Context) IThoGroupDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThoGroupDo
	WriteDB() IThoGroupDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThoGroupDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThoGroupDo
	Not(conds ...gen.Condition) IThoGroupDo
	Or(conds ...gen.Condition) IThoGroupDo
	Select(conds ...field.Expr) IThoGroupDo
	Where(conds ...gen.Condition) IThoGroupDo
	Order(conds ...field.Expr) IThoGroupDo
	Distinct(cols ...field.Expr) IThoGroupDo
	Omit(cols ...field.Expr) IThoGroupDo
	Join(table schema.Tabler, on ...field.Expr) IThoGroupDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThoGroupDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThoGroupDo
	Group(cols ...field.Expr) IThoGroupDo
	Having(conds ...gen.Condition) IThoGroupDo
	Limit(limit int) IThoGroupDo
	Offset(offset int) IThoGroupDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThoGroupDo
	Unscoped() IThoGroupDo
	Create(values ...*model.ThoGroup) error
	CreateInBatches(values []*model.ThoGroup, batchSize int) error
	Save(values ...*model.ThoGroup) error
	First() (*model.ThoGroup, error)
	Take() (*model.ThoGroup, error)
	Last() (*model.ThoGroup, error)
	Find() ([]*model.ThoGroup, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoGroup, err error)
	FindInBatches(result *[]*model.ThoGroup, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThoGroup) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThoGroupDo
	Assign(attrs ...field.AssignExpr) IThoGroupDo
	Joins(fields ...field.RelationField) IThoGroupDo
	Preload(fields ...field.RelationField) IThoGroupDo
	FirstOrInit() (*model.ThoGroup, error)
	FirstOrCreate() (*model.ThoGroup, error)
	FindByPage(offset int, limit int) (result []*model.ThoGroup, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThoGroupDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thoGroupDo) Debug() IThoGroupDo {
	return t.withDO(t.DO.Debug())
}

func (t thoGroupDo) WithContext(ctx context.Context) IThoGroupDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thoGroupDo) ReadDB() IThoGroupDo {
	return t.Clauses(dbresolver.Read)
}

func (t thoGroupDo) WriteDB() IThoGroupDo {
	return t.Clauses(dbresolver.Write)
}

func (t thoGroupDo) Session(config *gorm.Session) IThoGroupDo {
	return t.withDO(t.DO.Session(config))
}

func (t thoGroupDo) Clauses(conds ...clause.Expression) IThoGroupDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thoGroupDo) Returning(value interface{}, columns ...string) IThoGroupDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thoGroupDo) Not(conds ...gen.Condition) IThoGroupDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thoGroupDo) Or(conds ...gen.Condition) IThoGroupDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thoGroupDo) Select(conds ...field.Expr) IThoGroupDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thoGroupDo) Where(conds ...gen.Condition) IThoGroupDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thoGroupDo) Order(conds ...field.Expr) IThoGroupDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thoGroupDo) Distinct(cols ...field.Expr) IThoGroupDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thoGroupDo) Omit(cols ...field.Expr) IThoGroupDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thoGroupDo) Join(table schema.Tabler, on ...field.Expr) IThoGroupDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thoGroupDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThoGroupDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thoGroupDo) RightJoin(table schema.Tabler, on ...field.Expr) IThoGroupDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thoGroupDo) Group(cols ...field.Expr) IThoGroupDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thoGroupDo) Having(conds ...gen.Condition) IThoGroupDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thoGroupDo) Limit(limit int) IThoGroupDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thoGroupDo) Offset(offset int) IThoGroupDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thoGroupDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThoGroupDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thoGroupDo) Unscoped() IThoGroupDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thoGroupDo) Create(values ...*model.ThoGroup) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thoGroupDo) CreateInBatches(values []*model.ThoGroup, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thoGroupDo) Save(values ...*model.ThoGroup) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thoGroupDo) First() (*model.ThoGroup, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoGroup), nil
	}
}

func (t thoGroupDo) Take() (*model.ThoGroup, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoGroup), nil
	}
}

func (t thoGroupDo) Last() (*model.ThoGroup, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoGroup), nil
	}
}

func (t thoGroupDo) Find() ([]*model.ThoGroup, error) {
	result, err := t.DO.Find()
	return result.([]*model.ThoGroup), err
}

func (t thoGroupDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoGroup, err error) {
	buf := make([]*model.ThoGroup, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thoGroupDo) FindInBatches(result *[]*model.ThoGroup, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thoGroupDo) Attrs(attrs ...field.AssignExpr) IThoGroupDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thoGroupDo) Assign(attrs ...field.AssignExpr) IThoGroupDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thoGroupDo) Joins(fields ...field.RelationField) IThoGroupDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thoGroupDo) Preload(fields ...field.RelationField) IThoGroupDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thoGroupDo) FirstOrInit() (*model.ThoGroup, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoGroup), nil
	}
}

func (t thoGroupDo) FirstOrCreate() (*model.ThoGroup, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoGroup), nil
	}
}

func (t thoGroupDo) FindByPage(offset int, limit int) (result []*model.ThoGroup, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thoGroupDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thoGroupDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thoGroupDo) Delete(models ...*model.ThoGroup) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thoGroupDo) withDO(do gen.Dao) *thoGroupDo {
	t.DO = *do.(*gen.DO)
	return t
}
