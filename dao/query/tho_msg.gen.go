// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/wilsonce/rabbitmv_bot/dao/model"
)

func newThoMsg(db *gorm.DB, opts ...gen.DOOption) thoMsg {
	_thoMsg := thoMsg{}

	_thoMsg.thoMsgDo.UseDB(db, opts...)
	_thoMsg.thoMsgDo.UseModel(&model.ThoMsg{})

	tableName := _thoMsg.thoMsgDo.TableName()
	_thoMsg.ALL = field.NewAsterisk(tableName)
	_thoMsg.MsgID = field.NewInt32(tableName, "msg_id")
	_thoMsg.UserID = field.NewInt32(tableName, "user_id")
	_thoMsg.MsgType = field.NewInt8(tableName, "msg_type")
	_thoMsg.MsgStatus = field.NewInt8(tableName, "msg_status")
	_thoMsg.MsgTo = field.NewString(tableName, "msg_to")
	_thoMsg.MsgCode = field.NewString(tableName, "msg_code")
	_thoMsg.MsgContent = field.NewString(tableName, "msg_content")
	_thoMsg.MsgTime = field.NewInt32(tableName, "msg_time")

	_thoMsg.fillFieldMap()

	return _thoMsg
}

type thoMsg struct {
	thoMsgDo

	ALL        field.Asterisk
	MsgID      field.Int32
	UserID     field.Int32
	MsgType    field.Int8
	MsgStatus  field.Int8
	MsgTo      field.String
	MsgCode    field.String
	MsgContent field.String
	MsgTime    field.Int32

	fieldMap map[string]field.Expr
}

func (t thoMsg) Table(newTableName string) *thoMsg {
	t.thoMsgDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thoMsg) As(alias string) *thoMsg {
	t.thoMsgDo.DO = *(t.thoMsgDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thoMsg) updateTableName(table string) *thoMsg {
	t.ALL = field.NewAsterisk(table)
	t.MsgID = field.NewInt32(table, "msg_id")
	t.UserID = field.NewInt32(table, "user_id")
	t.MsgType = field.NewInt8(table, "msg_type")
	t.MsgStatus = field.NewInt8(table, "msg_status")
	t.MsgTo = field.NewString(table, "msg_to")
	t.MsgCode = field.NewString(table, "msg_code")
	t.MsgContent = field.NewString(table, "msg_content")
	t.MsgTime = field.NewInt32(table, "msg_time")

	t.fillFieldMap()

	return t
}

func (t *thoMsg) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thoMsg) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 8)
	t.fieldMap["msg_id"] = t.MsgID
	t.fieldMap["user_id"] = t.UserID
	t.fieldMap["msg_type"] = t.MsgType
	t.fieldMap["msg_status"] = t.MsgStatus
	t.fieldMap["msg_to"] = t.MsgTo
	t.fieldMap["msg_code"] = t.MsgCode
	t.fieldMap["msg_content"] = t.MsgContent
	t.fieldMap["msg_time"] = t.MsgTime
}

func (t thoMsg) clone(db *gorm.DB) thoMsg {
	t.thoMsgDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thoMsg) replaceDB(db *gorm.DB) thoMsg {
	t.thoMsgDo.ReplaceDB(db)
	return t
}

type thoMsgDo struct{ gen.DO }

type IThoMsgDo interface {
	gen.SubQuery
	Debug() IThoMsgDo
	WithContext(ctx context.Context) IThoMsgDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThoMsgDo
	WriteDB() IThoMsgDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThoMsgDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThoMsgDo
	Not(conds ...gen.Condition) IThoMsgDo
	Or(conds ...gen.Condition) IThoMsgDo
	Select(conds ...field.Expr) IThoMsgDo
	Where(conds ...gen.Condition) IThoMsgDo
	Order(conds ...field.Expr) IThoMsgDo
	Distinct(cols ...field.Expr) IThoMsgDo
	Omit(cols ...field.Expr) IThoMsgDo
	Join(table schema.Tabler, on ...field.Expr) IThoMsgDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThoMsgDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThoMsgDo
	Group(cols ...field.Expr) IThoMsgDo
	Having(conds ...gen.Condition) IThoMsgDo
	Limit(limit int) IThoMsgDo
	Offset(offset int) IThoMsgDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThoMsgDo
	Unscoped() IThoMsgDo
	Create(values ...*model.ThoMsg) error
	CreateInBatches(values []*model.ThoMsg, batchSize int) error
	Save(values ...*model.ThoMsg) error
	First() (*model.ThoMsg, error)
	Take() (*model.ThoMsg, error)
	Last() (*model.ThoMsg, error)
	Find() ([]*model.ThoMsg, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoMsg, err error)
	FindInBatches(result *[]*model.ThoMsg, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThoMsg) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThoMsgDo
	Assign(attrs ...field.AssignExpr) IThoMsgDo
	Joins(fields ...field.RelationField) IThoMsgDo
	Preload(fields ...field.RelationField) IThoMsgDo
	FirstOrInit() (*model.ThoMsg, error)
	FirstOrCreate() (*model.ThoMsg, error)
	FindByPage(offset int, limit int) (result []*model.ThoMsg, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThoMsgDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thoMsgDo) Debug() IThoMsgDo {
	return t.withDO(t.DO.Debug())
}

func (t thoMsgDo) WithContext(ctx context.Context) IThoMsgDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thoMsgDo) ReadDB() IThoMsgDo {
	return t.Clauses(dbresolver.Read)
}

func (t thoMsgDo) WriteDB() IThoMsgDo {
	return t.Clauses(dbresolver.Write)
}

func (t thoMsgDo) Session(config *gorm.Session) IThoMsgDo {
	return t.withDO(t.DO.Session(config))
}

func (t thoMsgDo) Clauses(conds ...clause.Expression) IThoMsgDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thoMsgDo) Returning(value interface{}, columns ...string) IThoMsgDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thoMsgDo) Not(conds ...gen.Condition) IThoMsgDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thoMsgDo) Or(conds ...gen.Condition) IThoMsgDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thoMsgDo) Select(conds ...field.Expr) IThoMsgDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thoMsgDo) Where(conds ...gen.Condition) IThoMsgDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thoMsgDo) Order(conds ...field.Expr) IThoMsgDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thoMsgDo) Distinct(cols ...field.Expr) IThoMsgDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thoMsgDo) Omit(cols ...field.Expr) IThoMsgDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thoMsgDo) Join(table schema.Tabler, on ...field.Expr) IThoMsgDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thoMsgDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThoMsgDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thoMsgDo) RightJoin(table schema.Tabler, on ...field.Expr) IThoMsgDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thoMsgDo) Group(cols ...field.Expr) IThoMsgDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thoMsgDo) Having(conds ...gen.Condition) IThoMsgDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thoMsgDo) Limit(limit int) IThoMsgDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thoMsgDo) Offset(offset int) IThoMsgDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thoMsgDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThoMsgDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thoMsgDo) Unscoped() IThoMsgDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thoMsgDo) Create(values ...*model.ThoMsg) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thoMsgDo) CreateInBatches(values []*model.ThoMsg, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thoMsgDo) Save(values ...*model.ThoMsg) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thoMsgDo) First() (*model.ThoMsg, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoMsg), nil
	}
}

func (t thoMsgDo) Take() (*model.ThoMsg, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoMsg), nil
	}
}

func (t thoMsgDo) Last() (*model.ThoMsg, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoMsg), nil
	}
}

func (t thoMsgDo) Find() ([]*model.ThoMsg, error) {
	result, err := t.DO.Find()
	return result.([]*model.ThoMsg), err
}

func (t thoMsgDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoMsg, err error) {
	buf := make([]*model.ThoMsg, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thoMsgDo) FindInBatches(result *[]*model.ThoMsg, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thoMsgDo) Attrs(attrs ...field.AssignExpr) IThoMsgDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thoMsgDo) Assign(attrs ...field.AssignExpr) IThoMsgDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thoMsgDo) Joins(fields ...field.RelationField) IThoMsgDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thoMsgDo) Preload(fields ...field.RelationField) IThoMsgDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thoMsgDo) FirstOrInit() (*model.ThoMsg, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoMsg), nil
	}
}

func (t thoMsgDo) FirstOrCreate() (*model.ThoMsg, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoMsg), nil
	}
}

func (t thoMsgDo) FindByPage(offset int, limit int) (result []*model.ThoMsg, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thoMsgDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thoMsgDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thoMsgDo) Delete(models ...*model.ThoMsg) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thoMsgDo) withDO(do gen.Dao) *thoMsgDo {
	t.DO = *do.(*gen.DO)
	return t
}
