// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/wilsonce/rabbitmv_bot/dao/model"
)

func newThoArt(db *gorm.DB, opts ...gen.DOOption) thoArt {
	_thoArt := thoArt{}

	_thoArt.thoArtDo.UseDB(db, opts...)
	_thoArt.thoArtDo.UseModel(&model.ThoArt{})

	tableName := _thoArt.thoArtDo.TableName()
	_thoArt.ALL = field.NewAsterisk(tableName)
	_thoArt.ArtID = field.NewInt32(tableName, "art_id")
	_thoArt.TypeID = field.NewInt32(tableName, "type_id")
	_thoArt.TypeId1 = field.NewInt32(tableName, "type_id_1")
	_thoArt.GroupID = field.NewInt32(tableName, "group_id")
	_thoArt.ArtName = field.NewString(tableName, "art_name")
	_thoArt.ArtSub = field.NewString(tableName, "art_sub")
	_thoArt.ArtEn = field.NewString(tableName, "art_en")
	_thoArt.ArtStatus = field.NewInt8(tableName, "art_status")
	_thoArt.ArtLetter = field.NewString(tableName, "art_letter")
	_thoArt.ArtColor = field.NewString(tableName, "art_color")
	_thoArt.ArtFrom = field.NewString(tableName, "art_from")
	_thoArt.ArtAuthor = field.NewString(tableName, "art_author")
	_thoArt.ArtTag = field.NewString(tableName, "art_tag")
	_thoArt.ArtClass = field.NewString(tableName, "art_class")
	_thoArt.ArtPic = field.NewString(tableName, "art_pic")
	_thoArt.ArtPicThumb = field.NewString(tableName, "art_pic_thumb")
	_thoArt.ArtPicSlide = field.NewString(tableName, "art_pic_slide")
	_thoArt.ArtPicScreenshot = field.NewString(tableName, "art_pic_screenshot")
	_thoArt.ArtBlurb = field.NewString(tableName, "art_blurb")
	_thoArt.ArtRemarks = field.NewString(tableName, "art_remarks")
	_thoArt.ArtJumpurl = field.NewString(tableName, "art_jumpurl")
	_thoArt.ArtTpl = field.NewString(tableName, "art_tpl")
	_thoArt.ArtLevel = field.NewInt8(tableName, "art_level")
	_thoArt.ArtLock = field.NewInt8(tableName, "art_lock")
	_thoArt.ArtPoints = field.NewInt32(tableName, "art_points")
	_thoArt.ArtPointsDetail = field.NewInt32(tableName, "art_points_detail")
	_thoArt.ArtUp = field.NewInt32(tableName, "art_up")
	_thoArt.ArtDown = field.NewInt32(tableName, "art_down")
	_thoArt.ArtHits = field.NewInt32(tableName, "art_hits")
	_thoArt.ArtHitsDay = field.NewInt32(tableName, "art_hits_day")
	_thoArt.ArtHitsWeek = field.NewInt32(tableName, "art_hits_week")
	_thoArt.ArtHitsMonth = field.NewInt32(tableName, "art_hits_month")
	_thoArt.ArtTime = field.NewInt32(tableName, "art_time")
	_thoArt.ArtTimeAdd = field.NewInt32(tableName, "art_time_add")
	_thoArt.ArtTimeHits = field.NewInt32(tableName, "art_time_hits")
	_thoArt.ArtTimeMake = field.NewInt32(tableName, "art_time_make")
	_thoArt.ArtScore = field.NewFloat64(tableName, "art_score")
	_thoArt.ArtScoreAll = field.NewInt32(tableName, "art_score_all")
	_thoArt.ArtScoreNum = field.NewInt32(tableName, "art_score_num")
	_thoArt.ArtRelArt = field.NewString(tableName, "art_rel_art")
	_thoArt.ArtRelVod = field.NewString(tableName, "art_rel_vod")
	_thoArt.ArtPwd = field.NewString(tableName, "art_pwd")
	_thoArt.ArtPwdURL = field.NewString(tableName, "art_pwd_url")
	_thoArt.ArtTitle = field.NewString(tableName, "art_title")
	_thoArt.ArtNote = field.NewString(tableName, "art_note")
	_thoArt.ArtContent = field.NewString(tableName, "art_content")

	_thoArt.fillFieldMap()

	return _thoArt
}

type thoArt struct {
	thoArtDo

	ALL              field.Asterisk
	ArtID            field.Int32
	TypeID           field.Int32
	TypeId1          field.Int32
	GroupID          field.Int32
	ArtName          field.String
	ArtSub           field.String
	ArtEn            field.String
	ArtStatus        field.Int8
	ArtLetter        field.String
	ArtColor         field.String
	ArtFrom          field.String
	ArtAuthor        field.String
	ArtTag           field.String
	ArtClass         field.String
	ArtPic           field.String
	ArtPicThumb      field.String
	ArtPicSlide      field.String
	ArtPicScreenshot field.String
	ArtBlurb         field.String
	ArtRemarks       field.String
	ArtJumpurl       field.String
	ArtTpl           field.String
	ArtLevel         field.Int8
	ArtLock          field.Int8
	ArtPoints        field.Int32
	ArtPointsDetail  field.Int32
	ArtUp            field.Int32
	ArtDown          field.Int32
	ArtHits          field.Int32
	ArtHitsDay       field.Int32
	ArtHitsWeek      field.Int32
	ArtHitsMonth     field.Int32
	ArtTime          field.Int32
	ArtTimeAdd       field.Int32
	ArtTimeHits      field.Int32
	ArtTimeMake      field.Int32
	ArtScore         field.Float64
	ArtScoreAll      field.Int32
	ArtScoreNum      field.Int32
	ArtRelArt        field.String
	ArtRelVod        field.String
	ArtPwd           field.String
	ArtPwdURL        field.String
	ArtTitle         field.String
	ArtNote          field.String
	ArtContent       field.String

	fieldMap map[string]field.Expr
}

func (t thoArt) Table(newTableName string) *thoArt {
	t.thoArtDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thoArt) As(alias string) *thoArt {
	t.thoArtDo.DO = *(t.thoArtDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thoArt) updateTableName(table string) *thoArt {
	t.ALL = field.NewAsterisk(table)
	t.ArtID = field.NewInt32(table, "art_id")
	t.TypeID = field.NewInt32(table, "type_id")
	t.TypeId1 = field.NewInt32(table, "type_id_1")
	t.GroupID = field.NewInt32(table, "group_id")
	t.ArtName = field.NewString(table, "art_name")
	t.ArtSub = field.NewString(table, "art_sub")
	t.ArtEn = field.NewString(table, "art_en")
	t.ArtStatus = field.NewInt8(table, "art_status")
	t.ArtLetter = field.NewString(table, "art_letter")
	t.ArtColor = field.NewString(table, "art_color")
	t.ArtFrom = field.NewString(table, "art_from")
	t.ArtAuthor = field.NewString(table, "art_author")
	t.ArtTag = field.NewString(table, "art_tag")
	t.ArtClass = field.NewString(table, "art_class")
	t.ArtPic = field.NewString(table, "art_pic")
	t.ArtPicThumb = field.NewString(table, "art_pic_thumb")
	t.ArtPicSlide = field.NewString(table, "art_pic_slide")
	t.ArtPicScreenshot = field.NewString(table, "art_pic_screenshot")
	t.ArtBlurb = field.NewString(table, "art_blurb")
	t.ArtRemarks = field.NewString(table, "art_remarks")
	t.ArtJumpurl = field.NewString(table, "art_jumpurl")
	t.ArtTpl = field.NewString(table, "art_tpl")
	t.ArtLevel = field.NewInt8(table, "art_level")
	t.ArtLock = field.NewInt8(table, "art_lock")
	t.ArtPoints = field.NewInt32(table, "art_points")
	t.ArtPointsDetail = field.NewInt32(table, "art_points_detail")
	t.ArtUp = field.NewInt32(table, "art_up")
	t.ArtDown = field.NewInt32(table, "art_down")
	t.ArtHits = field.NewInt32(table, "art_hits")
	t.ArtHitsDay = field.NewInt32(table, "art_hits_day")
	t.ArtHitsWeek = field.NewInt32(table, "art_hits_week")
	t.ArtHitsMonth = field.NewInt32(table, "art_hits_month")
	t.ArtTime = field.NewInt32(table, "art_time")
	t.ArtTimeAdd = field.NewInt32(table, "art_time_add")
	t.ArtTimeHits = field.NewInt32(table, "art_time_hits")
	t.ArtTimeMake = field.NewInt32(table, "art_time_make")
	t.ArtScore = field.NewFloat64(table, "art_score")
	t.ArtScoreAll = field.NewInt32(table, "art_score_all")
	t.ArtScoreNum = field.NewInt32(table, "art_score_num")
	t.ArtRelArt = field.NewString(table, "art_rel_art")
	t.ArtRelVod = field.NewString(table, "art_rel_vod")
	t.ArtPwd = field.NewString(table, "art_pwd")
	t.ArtPwdURL = field.NewString(table, "art_pwd_url")
	t.ArtTitle = field.NewString(table, "art_title")
	t.ArtNote = field.NewString(table, "art_note")
	t.ArtContent = field.NewString(table, "art_content")

	t.fillFieldMap()

	return t
}

func (t *thoArt) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thoArt) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 46)
	t.fieldMap["art_id"] = t.ArtID
	t.fieldMap["type_id"] = t.TypeID
	t.fieldMap["type_id_1"] = t.TypeId1
	t.fieldMap["group_id"] = t.GroupID
	t.fieldMap["art_name"] = t.ArtName
	t.fieldMap["art_sub"] = t.ArtSub
	t.fieldMap["art_en"] = t.ArtEn
	t.fieldMap["art_status"] = t.ArtStatus
	t.fieldMap["art_letter"] = t.ArtLetter
	t.fieldMap["art_color"] = t.ArtColor
	t.fieldMap["art_from"] = t.ArtFrom
	t.fieldMap["art_author"] = t.ArtAuthor
	t.fieldMap["art_tag"] = t.ArtTag
	t.fieldMap["art_class"] = t.ArtClass
	t.fieldMap["art_pic"] = t.ArtPic
	t.fieldMap["art_pic_thumb"] = t.ArtPicThumb
	t.fieldMap["art_pic_slide"] = t.ArtPicSlide
	t.fieldMap["art_pic_screenshot"] = t.ArtPicScreenshot
	t.fieldMap["art_blurb"] = t.ArtBlurb
	t.fieldMap["art_remarks"] = t.ArtRemarks
	t.fieldMap["art_jumpurl"] = t.ArtJumpurl
	t.fieldMap["art_tpl"] = t.ArtTpl
	t.fieldMap["art_level"] = t.ArtLevel
	t.fieldMap["art_lock"] = t.ArtLock
	t.fieldMap["art_points"] = t.ArtPoints
	t.fieldMap["art_points_detail"] = t.ArtPointsDetail
	t.fieldMap["art_up"] = t.ArtUp
	t.fieldMap["art_down"] = t.ArtDown
	t.fieldMap["art_hits"] = t.ArtHits
	t.fieldMap["art_hits_day"] = t.ArtHitsDay
	t.fieldMap["art_hits_week"] = t.ArtHitsWeek
	t.fieldMap["art_hits_month"] = t.ArtHitsMonth
	t.fieldMap["art_time"] = t.ArtTime
	t.fieldMap["art_time_add"] = t.ArtTimeAdd
	t.fieldMap["art_time_hits"] = t.ArtTimeHits
	t.fieldMap["art_time_make"] = t.ArtTimeMake
	t.fieldMap["art_score"] = t.ArtScore
	t.fieldMap["art_score_all"] = t.ArtScoreAll
	t.fieldMap["art_score_num"] = t.ArtScoreNum
	t.fieldMap["art_rel_art"] = t.ArtRelArt
	t.fieldMap["art_rel_vod"] = t.ArtRelVod
	t.fieldMap["art_pwd"] = t.ArtPwd
	t.fieldMap["art_pwd_url"] = t.ArtPwdURL
	t.fieldMap["art_title"] = t.ArtTitle
	t.fieldMap["art_note"] = t.ArtNote
	t.fieldMap["art_content"] = t.ArtContent
}

func (t thoArt) clone(db *gorm.DB) thoArt {
	t.thoArtDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thoArt) replaceDB(db *gorm.DB) thoArt {
	t.thoArtDo.ReplaceDB(db)
	return t
}

type thoArtDo struct{ gen.DO }

type IThoArtDo interface {
	gen.SubQuery
	Debug() IThoArtDo
	WithContext(ctx context.Context) IThoArtDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThoArtDo
	WriteDB() IThoArtDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThoArtDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThoArtDo
	Not(conds ...gen.Condition) IThoArtDo
	Or(conds ...gen.Condition) IThoArtDo
	Select(conds ...field.Expr) IThoArtDo
	Where(conds ...gen.Condition) IThoArtDo
	Order(conds ...field.Expr) IThoArtDo
	Distinct(cols ...field.Expr) IThoArtDo
	Omit(cols ...field.Expr) IThoArtDo
	Join(table schema.Tabler, on ...field.Expr) IThoArtDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThoArtDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThoArtDo
	Group(cols ...field.Expr) IThoArtDo
	Having(conds ...gen.Condition) IThoArtDo
	Limit(limit int) IThoArtDo
	Offset(offset int) IThoArtDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThoArtDo
	Unscoped() IThoArtDo
	Create(values ...*model.ThoArt) error
	CreateInBatches(values []*model.ThoArt, batchSize int) error
	Save(values ...*model.ThoArt) error
	First() (*model.ThoArt, error)
	Take() (*model.ThoArt, error)
	Last() (*model.ThoArt, error)
	Find() ([]*model.ThoArt, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoArt, err error)
	FindInBatches(result *[]*model.ThoArt, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThoArt) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThoArtDo
	Assign(attrs ...field.AssignExpr) IThoArtDo
	Joins(fields ...field.RelationField) IThoArtDo
	Preload(fields ...field.RelationField) IThoArtDo
	FirstOrInit() (*model.ThoArt, error)
	FirstOrCreate() (*model.ThoArt, error)
	FindByPage(offset int, limit int) (result []*model.ThoArt, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThoArtDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thoArtDo) Debug() IThoArtDo {
	return t.withDO(t.DO.Debug())
}

func (t thoArtDo) WithContext(ctx context.Context) IThoArtDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thoArtDo) ReadDB() IThoArtDo {
	return t.Clauses(dbresolver.Read)
}

func (t thoArtDo) WriteDB() IThoArtDo {
	return t.Clauses(dbresolver.Write)
}

func (t thoArtDo) Session(config *gorm.Session) IThoArtDo {
	return t.withDO(t.DO.Session(config))
}

func (t thoArtDo) Clauses(conds ...clause.Expression) IThoArtDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thoArtDo) Returning(value interface{}, columns ...string) IThoArtDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thoArtDo) Not(conds ...gen.Condition) IThoArtDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thoArtDo) Or(conds ...gen.Condition) IThoArtDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thoArtDo) Select(conds ...field.Expr) IThoArtDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thoArtDo) Where(conds ...gen.Condition) IThoArtDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thoArtDo) Order(conds ...field.Expr) IThoArtDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thoArtDo) Distinct(cols ...field.Expr) IThoArtDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thoArtDo) Omit(cols ...field.Expr) IThoArtDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thoArtDo) Join(table schema.Tabler, on ...field.Expr) IThoArtDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thoArtDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThoArtDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thoArtDo) RightJoin(table schema.Tabler, on ...field.Expr) IThoArtDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thoArtDo) Group(cols ...field.Expr) IThoArtDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thoArtDo) Having(conds ...gen.Condition) IThoArtDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thoArtDo) Limit(limit int) IThoArtDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thoArtDo) Offset(offset int) IThoArtDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thoArtDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThoArtDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thoArtDo) Unscoped() IThoArtDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thoArtDo) Create(values ...*model.ThoArt) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thoArtDo) CreateInBatches(values []*model.ThoArt, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thoArtDo) Save(values ...*model.ThoArt) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thoArtDo) First() (*model.ThoArt, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoArt), nil
	}
}

func (t thoArtDo) Take() (*model.ThoArt, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoArt), nil
	}
}

func (t thoArtDo) Last() (*model.ThoArt, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoArt), nil
	}
}

func (t thoArtDo) Find() ([]*model.ThoArt, error) {
	result, err := t.DO.Find()
	return result.([]*model.ThoArt), err
}

func (t thoArtDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoArt, err error) {
	buf := make([]*model.ThoArt, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thoArtDo) FindInBatches(result *[]*model.ThoArt, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thoArtDo) Attrs(attrs ...field.AssignExpr) IThoArtDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thoArtDo) Assign(attrs ...field.AssignExpr) IThoArtDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thoArtDo) Joins(fields ...field.RelationField) IThoArtDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thoArtDo) Preload(fields ...field.RelationField) IThoArtDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thoArtDo) FirstOrInit() (*model.ThoArt, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoArt), nil
	}
}

func (t thoArtDo) FirstOrCreate() (*model.ThoArt, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoArt), nil
	}
}

func (t thoArtDo) FindByPage(offset int, limit int) (result []*model.ThoArt, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thoArtDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thoArtDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thoArtDo) Delete(models ...*model.ThoArt) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thoArtDo) withDO(do gen.Dao) *thoArtDo {
	t.DO = *do.(*gen.DO)
	return t
}
