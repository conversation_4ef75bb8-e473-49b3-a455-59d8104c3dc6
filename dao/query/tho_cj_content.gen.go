// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/wilsonce/rabbitmv_bot/dao/model"
)

func newThoCjContent(db *gorm.DB, opts ...gen.DOOption) thoCjContent {
	_thoCjContent := thoCjContent{}

	_thoCjContent.thoCjContentDo.UseDB(db, opts...)
	_thoCjContent.thoCjContentDo.UseModel(&model.ThoCjContent{})

	tableName := _thoCjContent.thoCjContentDo.TableName()
	_thoCjContent.ALL = field.NewAsterisk(tableName)
	_thoCjContent.ID = field.NewInt32(tableName, "id")
	_thoCjContent.Nodeid = field.NewInt32(tableName, "nodeid")
	_thoCjContent.Status = field.NewInt8(tableName, "status")
	_thoCjContent.URL = field.NewString(tableName, "url")
	_thoCjContent.Title = field.NewString(tableName, "title")
	_thoCjContent.Data = field.NewString(tableName, "data")

	_thoCjContent.fillFieldMap()

	return _thoCjContent
}

type thoCjContent struct {
	thoCjContentDo

	ALL    field.Asterisk
	ID     field.Int32
	Nodeid field.Int32
	Status field.Int8
	URL    field.String
	Title  field.String
	Data   field.String

	fieldMap map[string]field.Expr
}

func (t thoCjContent) Table(newTableName string) *thoCjContent {
	t.thoCjContentDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thoCjContent) As(alias string) *thoCjContent {
	t.thoCjContentDo.DO = *(t.thoCjContentDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thoCjContent) updateTableName(table string) *thoCjContent {
	t.ALL = field.NewAsterisk(table)
	t.ID = field.NewInt32(table, "id")
	t.Nodeid = field.NewInt32(table, "nodeid")
	t.Status = field.NewInt8(table, "status")
	t.URL = field.NewString(table, "url")
	t.Title = field.NewString(table, "title")
	t.Data = field.NewString(table, "data")

	t.fillFieldMap()

	return t
}

func (t *thoCjContent) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thoCjContent) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 6)
	t.fieldMap["id"] = t.ID
	t.fieldMap["nodeid"] = t.Nodeid
	t.fieldMap["status"] = t.Status
	t.fieldMap["url"] = t.URL
	t.fieldMap["title"] = t.Title
	t.fieldMap["data"] = t.Data
}

func (t thoCjContent) clone(db *gorm.DB) thoCjContent {
	t.thoCjContentDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thoCjContent) replaceDB(db *gorm.DB) thoCjContent {
	t.thoCjContentDo.ReplaceDB(db)
	return t
}

type thoCjContentDo struct{ gen.DO }

type IThoCjContentDo interface {
	gen.SubQuery
	Debug() IThoCjContentDo
	WithContext(ctx context.Context) IThoCjContentDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThoCjContentDo
	WriteDB() IThoCjContentDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThoCjContentDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThoCjContentDo
	Not(conds ...gen.Condition) IThoCjContentDo
	Or(conds ...gen.Condition) IThoCjContentDo
	Select(conds ...field.Expr) IThoCjContentDo
	Where(conds ...gen.Condition) IThoCjContentDo
	Order(conds ...field.Expr) IThoCjContentDo
	Distinct(cols ...field.Expr) IThoCjContentDo
	Omit(cols ...field.Expr) IThoCjContentDo
	Join(table schema.Tabler, on ...field.Expr) IThoCjContentDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThoCjContentDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThoCjContentDo
	Group(cols ...field.Expr) IThoCjContentDo
	Having(conds ...gen.Condition) IThoCjContentDo
	Limit(limit int) IThoCjContentDo
	Offset(offset int) IThoCjContentDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThoCjContentDo
	Unscoped() IThoCjContentDo
	Create(values ...*model.ThoCjContent) error
	CreateInBatches(values []*model.ThoCjContent, batchSize int) error
	Save(values ...*model.ThoCjContent) error
	First() (*model.ThoCjContent, error)
	Take() (*model.ThoCjContent, error)
	Last() (*model.ThoCjContent, error)
	Find() ([]*model.ThoCjContent, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoCjContent, err error)
	FindInBatches(result *[]*model.ThoCjContent, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThoCjContent) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThoCjContentDo
	Assign(attrs ...field.AssignExpr) IThoCjContentDo
	Joins(fields ...field.RelationField) IThoCjContentDo
	Preload(fields ...field.RelationField) IThoCjContentDo
	FirstOrInit() (*model.ThoCjContent, error)
	FirstOrCreate() (*model.ThoCjContent, error)
	FindByPage(offset int, limit int) (result []*model.ThoCjContent, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThoCjContentDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thoCjContentDo) Debug() IThoCjContentDo {
	return t.withDO(t.DO.Debug())
}

func (t thoCjContentDo) WithContext(ctx context.Context) IThoCjContentDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thoCjContentDo) ReadDB() IThoCjContentDo {
	return t.Clauses(dbresolver.Read)
}

func (t thoCjContentDo) WriteDB() IThoCjContentDo {
	return t.Clauses(dbresolver.Write)
}

func (t thoCjContentDo) Session(config *gorm.Session) IThoCjContentDo {
	return t.withDO(t.DO.Session(config))
}

func (t thoCjContentDo) Clauses(conds ...clause.Expression) IThoCjContentDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thoCjContentDo) Returning(value interface{}, columns ...string) IThoCjContentDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thoCjContentDo) Not(conds ...gen.Condition) IThoCjContentDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thoCjContentDo) Or(conds ...gen.Condition) IThoCjContentDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thoCjContentDo) Select(conds ...field.Expr) IThoCjContentDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thoCjContentDo) Where(conds ...gen.Condition) IThoCjContentDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thoCjContentDo) Order(conds ...field.Expr) IThoCjContentDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thoCjContentDo) Distinct(cols ...field.Expr) IThoCjContentDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thoCjContentDo) Omit(cols ...field.Expr) IThoCjContentDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thoCjContentDo) Join(table schema.Tabler, on ...field.Expr) IThoCjContentDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thoCjContentDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThoCjContentDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thoCjContentDo) RightJoin(table schema.Tabler, on ...field.Expr) IThoCjContentDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thoCjContentDo) Group(cols ...field.Expr) IThoCjContentDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thoCjContentDo) Having(conds ...gen.Condition) IThoCjContentDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thoCjContentDo) Limit(limit int) IThoCjContentDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thoCjContentDo) Offset(offset int) IThoCjContentDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thoCjContentDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThoCjContentDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thoCjContentDo) Unscoped() IThoCjContentDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thoCjContentDo) Create(values ...*model.ThoCjContent) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thoCjContentDo) CreateInBatches(values []*model.ThoCjContent, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thoCjContentDo) Save(values ...*model.ThoCjContent) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thoCjContentDo) First() (*model.ThoCjContent, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoCjContent), nil
	}
}

func (t thoCjContentDo) Take() (*model.ThoCjContent, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoCjContent), nil
	}
}

func (t thoCjContentDo) Last() (*model.ThoCjContent, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoCjContent), nil
	}
}

func (t thoCjContentDo) Find() ([]*model.ThoCjContent, error) {
	result, err := t.DO.Find()
	return result.([]*model.ThoCjContent), err
}

func (t thoCjContentDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoCjContent, err error) {
	buf := make([]*model.ThoCjContent, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thoCjContentDo) FindInBatches(result *[]*model.ThoCjContent, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thoCjContentDo) Attrs(attrs ...field.AssignExpr) IThoCjContentDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thoCjContentDo) Assign(attrs ...field.AssignExpr) IThoCjContentDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thoCjContentDo) Joins(fields ...field.RelationField) IThoCjContentDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thoCjContentDo) Preload(fields ...field.RelationField) IThoCjContentDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thoCjContentDo) FirstOrInit() (*model.ThoCjContent, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoCjContent), nil
	}
}

func (t thoCjContentDo) FirstOrCreate() (*model.ThoCjContent, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoCjContent), nil
	}
}

func (t thoCjContentDo) FindByPage(offset int, limit int) (result []*model.ThoCjContent, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thoCjContentDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thoCjContentDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thoCjContentDo) Delete(models ...*model.ThoCjContent) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thoCjContentDo) withDO(do gen.Dao) *thoCjContentDo {
	t.DO = *do.(*gen.DO)
	return t
}
