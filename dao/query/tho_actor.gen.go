// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/wilsonce/rabbitmv_bot/dao/model"
)

func newThoActor(db *gorm.DB, opts ...gen.DOOption) thoActor {
	_thoActor := thoActor{}

	_thoActor.thoActorDo.UseDB(db, opts...)
	_thoActor.thoActorDo.UseModel(&model.ThoActor{})

	tableName := _thoActor.thoActorDo.TableName()
	_thoActor.ALL = field.NewAsterisk(tableName)
	_thoActor.ActorID = field.NewInt32(tableName, "actor_id")
	_thoActor.TypeID = field.NewInt32(tableName, "type_id")
	_thoActor.TypeId1 = field.NewInt32(tableName, "type_id_1")
	_thoActor.ActorName = field.NewString(tableName, "actor_name")
	_thoActor.ActorEn = field.NewString(tableName, "actor_en")
	_thoActor.ActorAlias = field.NewString(tableName, "actor_alias")
	_thoActor.ActorStatus = field.NewInt8(tableName, "actor_status")
	_thoActor.ActorLock = field.NewInt8(tableName, "actor_lock")
	_thoActor.ActorLetter = field.NewString(tableName, "actor_letter")
	_thoActor.ActorSex = field.NewString(tableName, "actor_sex")
	_thoActor.ActorColor = field.NewString(tableName, "actor_color")
	_thoActor.ActorPic = field.NewString(tableName, "actor_pic")
	_thoActor.ActorBlurb = field.NewString(tableName, "actor_blurb")
	_thoActor.ActorRemarks = field.NewString(tableName, "actor_remarks")
	_thoActor.ActorArea = field.NewString(tableName, "actor_area")
	_thoActor.ActorHeight = field.NewString(tableName, "actor_height")
	_thoActor.ActorWeight = field.NewString(tableName, "actor_weight")
	_thoActor.ActorBirthday = field.NewString(tableName, "actor_birthday")
	_thoActor.ActorBirtharea = field.NewString(tableName, "actor_birtharea")
	_thoActor.ActorBlood = field.NewString(tableName, "actor_blood")
	_thoActor.ActorStarsign = field.NewString(tableName, "actor_starsign")
	_thoActor.ActorSchool = field.NewString(tableName, "actor_school")
	_thoActor.ActorWorks = field.NewString(tableName, "actor_works")
	_thoActor.ActorTag = field.NewString(tableName, "actor_tag")
	_thoActor.ActorClass = field.NewString(tableName, "actor_class")
	_thoActor.ActorLevel = field.NewInt8(tableName, "actor_level")
	_thoActor.ActorTime = field.NewInt32(tableName, "actor_time")
	_thoActor.ActorTimeAdd = field.NewInt32(tableName, "actor_time_add")
	_thoActor.ActorTimeHits = field.NewInt32(tableName, "actor_time_hits")
	_thoActor.ActorTimeMake = field.NewInt32(tableName, "actor_time_make")
	_thoActor.ActorHits = field.NewInt32(tableName, "actor_hits")
	_thoActor.ActorHitsDay = field.NewInt32(tableName, "actor_hits_day")
	_thoActor.ActorHitsWeek = field.NewInt32(tableName, "actor_hits_week")
	_thoActor.ActorHitsMonth = field.NewInt32(tableName, "actor_hits_month")
	_thoActor.ActorScore = field.NewFloat64(tableName, "actor_score")
	_thoActor.ActorScoreAll = field.NewInt32(tableName, "actor_score_all")
	_thoActor.ActorScoreNum = field.NewInt32(tableName, "actor_score_num")
	_thoActor.ActorUp = field.NewInt32(tableName, "actor_up")
	_thoActor.ActorDown = field.NewInt32(tableName, "actor_down")
	_thoActor.ActorTpl = field.NewString(tableName, "actor_tpl")
	_thoActor.ActorJumpurl = field.NewString(tableName, "actor_jumpurl")
	_thoActor.ActorContent = field.NewString(tableName, "actor_content")

	_thoActor.fillFieldMap()

	return _thoActor
}

type thoActor struct {
	thoActorDo

	ALL            field.Asterisk
	ActorID        field.Int32
	TypeID         field.Int32
	TypeId1        field.Int32
	ActorName      field.String
	ActorEn        field.String
	ActorAlias     field.String
	ActorStatus    field.Int8
	ActorLock      field.Int8
	ActorLetter    field.String
	ActorSex       field.String
	ActorColor     field.String
	ActorPic       field.String
	ActorBlurb     field.String
	ActorRemarks   field.String
	ActorArea      field.String
	ActorHeight    field.String
	ActorWeight    field.String
	ActorBirthday  field.String
	ActorBirtharea field.String
	ActorBlood     field.String
	ActorStarsign  field.String
	ActorSchool    field.String
	ActorWorks     field.String
	ActorTag       field.String
	ActorClass     field.String
	ActorLevel     field.Int8
	ActorTime      field.Int32
	ActorTimeAdd   field.Int32
	ActorTimeHits  field.Int32
	ActorTimeMake  field.Int32
	ActorHits      field.Int32
	ActorHitsDay   field.Int32
	ActorHitsWeek  field.Int32
	ActorHitsMonth field.Int32
	ActorScore     field.Float64
	ActorScoreAll  field.Int32
	ActorScoreNum  field.Int32
	ActorUp        field.Int32
	ActorDown      field.Int32
	ActorTpl       field.String
	ActorJumpurl   field.String
	ActorContent   field.String

	fieldMap map[string]field.Expr
}

func (t thoActor) Table(newTableName string) *thoActor {
	t.thoActorDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thoActor) As(alias string) *thoActor {
	t.thoActorDo.DO = *(t.thoActorDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thoActor) updateTableName(table string) *thoActor {
	t.ALL = field.NewAsterisk(table)
	t.ActorID = field.NewInt32(table, "actor_id")
	t.TypeID = field.NewInt32(table, "type_id")
	t.TypeId1 = field.NewInt32(table, "type_id_1")
	t.ActorName = field.NewString(table, "actor_name")
	t.ActorEn = field.NewString(table, "actor_en")
	t.ActorAlias = field.NewString(table, "actor_alias")
	t.ActorStatus = field.NewInt8(table, "actor_status")
	t.ActorLock = field.NewInt8(table, "actor_lock")
	t.ActorLetter = field.NewString(table, "actor_letter")
	t.ActorSex = field.NewString(table, "actor_sex")
	t.ActorColor = field.NewString(table, "actor_color")
	t.ActorPic = field.NewString(table, "actor_pic")
	t.ActorBlurb = field.NewString(table, "actor_blurb")
	t.ActorRemarks = field.NewString(table, "actor_remarks")
	t.ActorArea = field.NewString(table, "actor_area")
	t.ActorHeight = field.NewString(table, "actor_height")
	t.ActorWeight = field.NewString(table, "actor_weight")
	t.ActorBirthday = field.NewString(table, "actor_birthday")
	t.ActorBirtharea = field.NewString(table, "actor_birtharea")
	t.ActorBlood = field.NewString(table, "actor_blood")
	t.ActorStarsign = field.NewString(table, "actor_starsign")
	t.ActorSchool = field.NewString(table, "actor_school")
	t.ActorWorks = field.NewString(table, "actor_works")
	t.ActorTag = field.NewString(table, "actor_tag")
	t.ActorClass = field.NewString(table, "actor_class")
	t.ActorLevel = field.NewInt8(table, "actor_level")
	t.ActorTime = field.NewInt32(table, "actor_time")
	t.ActorTimeAdd = field.NewInt32(table, "actor_time_add")
	t.ActorTimeHits = field.NewInt32(table, "actor_time_hits")
	t.ActorTimeMake = field.NewInt32(table, "actor_time_make")
	t.ActorHits = field.NewInt32(table, "actor_hits")
	t.ActorHitsDay = field.NewInt32(table, "actor_hits_day")
	t.ActorHitsWeek = field.NewInt32(table, "actor_hits_week")
	t.ActorHitsMonth = field.NewInt32(table, "actor_hits_month")
	t.ActorScore = field.NewFloat64(table, "actor_score")
	t.ActorScoreAll = field.NewInt32(table, "actor_score_all")
	t.ActorScoreNum = field.NewInt32(table, "actor_score_num")
	t.ActorUp = field.NewInt32(table, "actor_up")
	t.ActorDown = field.NewInt32(table, "actor_down")
	t.ActorTpl = field.NewString(table, "actor_tpl")
	t.ActorJumpurl = field.NewString(table, "actor_jumpurl")
	t.ActorContent = field.NewString(table, "actor_content")

	t.fillFieldMap()

	return t
}

func (t *thoActor) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thoActor) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 42)
	t.fieldMap["actor_id"] = t.ActorID
	t.fieldMap["type_id"] = t.TypeID
	t.fieldMap["type_id_1"] = t.TypeId1
	t.fieldMap["actor_name"] = t.ActorName
	t.fieldMap["actor_en"] = t.ActorEn
	t.fieldMap["actor_alias"] = t.ActorAlias
	t.fieldMap["actor_status"] = t.ActorStatus
	t.fieldMap["actor_lock"] = t.ActorLock
	t.fieldMap["actor_letter"] = t.ActorLetter
	t.fieldMap["actor_sex"] = t.ActorSex
	t.fieldMap["actor_color"] = t.ActorColor
	t.fieldMap["actor_pic"] = t.ActorPic
	t.fieldMap["actor_blurb"] = t.ActorBlurb
	t.fieldMap["actor_remarks"] = t.ActorRemarks
	t.fieldMap["actor_area"] = t.ActorArea
	t.fieldMap["actor_height"] = t.ActorHeight
	t.fieldMap["actor_weight"] = t.ActorWeight
	t.fieldMap["actor_birthday"] = t.ActorBirthday
	t.fieldMap["actor_birtharea"] = t.ActorBirtharea
	t.fieldMap["actor_blood"] = t.ActorBlood
	t.fieldMap["actor_starsign"] = t.ActorStarsign
	t.fieldMap["actor_school"] = t.ActorSchool
	t.fieldMap["actor_works"] = t.ActorWorks
	t.fieldMap["actor_tag"] = t.ActorTag
	t.fieldMap["actor_class"] = t.ActorClass
	t.fieldMap["actor_level"] = t.ActorLevel
	t.fieldMap["actor_time"] = t.ActorTime
	t.fieldMap["actor_time_add"] = t.ActorTimeAdd
	t.fieldMap["actor_time_hits"] = t.ActorTimeHits
	t.fieldMap["actor_time_make"] = t.ActorTimeMake
	t.fieldMap["actor_hits"] = t.ActorHits
	t.fieldMap["actor_hits_day"] = t.ActorHitsDay
	t.fieldMap["actor_hits_week"] = t.ActorHitsWeek
	t.fieldMap["actor_hits_month"] = t.ActorHitsMonth
	t.fieldMap["actor_score"] = t.ActorScore
	t.fieldMap["actor_score_all"] = t.ActorScoreAll
	t.fieldMap["actor_score_num"] = t.ActorScoreNum
	t.fieldMap["actor_up"] = t.ActorUp
	t.fieldMap["actor_down"] = t.ActorDown
	t.fieldMap["actor_tpl"] = t.ActorTpl
	t.fieldMap["actor_jumpurl"] = t.ActorJumpurl
	t.fieldMap["actor_content"] = t.ActorContent
}

func (t thoActor) clone(db *gorm.DB) thoActor {
	t.thoActorDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thoActor) replaceDB(db *gorm.DB) thoActor {
	t.thoActorDo.ReplaceDB(db)
	return t
}

type thoActorDo struct{ gen.DO }

type IThoActorDo interface {
	gen.SubQuery
	Debug() IThoActorDo
	WithContext(ctx context.Context) IThoActorDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThoActorDo
	WriteDB() IThoActorDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThoActorDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThoActorDo
	Not(conds ...gen.Condition) IThoActorDo
	Or(conds ...gen.Condition) IThoActorDo
	Select(conds ...field.Expr) IThoActorDo
	Where(conds ...gen.Condition) IThoActorDo
	Order(conds ...field.Expr) IThoActorDo
	Distinct(cols ...field.Expr) IThoActorDo
	Omit(cols ...field.Expr) IThoActorDo
	Join(table schema.Tabler, on ...field.Expr) IThoActorDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThoActorDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThoActorDo
	Group(cols ...field.Expr) IThoActorDo
	Having(conds ...gen.Condition) IThoActorDo
	Limit(limit int) IThoActorDo
	Offset(offset int) IThoActorDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThoActorDo
	Unscoped() IThoActorDo
	Create(values ...*model.ThoActor) error
	CreateInBatches(values []*model.ThoActor, batchSize int) error
	Save(values ...*model.ThoActor) error
	First() (*model.ThoActor, error)
	Take() (*model.ThoActor, error)
	Last() (*model.ThoActor, error)
	Find() ([]*model.ThoActor, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoActor, err error)
	FindInBatches(result *[]*model.ThoActor, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThoActor) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThoActorDo
	Assign(attrs ...field.AssignExpr) IThoActorDo
	Joins(fields ...field.RelationField) IThoActorDo
	Preload(fields ...field.RelationField) IThoActorDo
	FirstOrInit() (*model.ThoActor, error)
	FirstOrCreate() (*model.ThoActor, error)
	FindByPage(offset int, limit int) (result []*model.ThoActor, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThoActorDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thoActorDo) Debug() IThoActorDo {
	return t.withDO(t.DO.Debug())
}

func (t thoActorDo) WithContext(ctx context.Context) IThoActorDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thoActorDo) ReadDB() IThoActorDo {
	return t.Clauses(dbresolver.Read)
}

func (t thoActorDo) WriteDB() IThoActorDo {
	return t.Clauses(dbresolver.Write)
}

func (t thoActorDo) Session(config *gorm.Session) IThoActorDo {
	return t.withDO(t.DO.Session(config))
}

func (t thoActorDo) Clauses(conds ...clause.Expression) IThoActorDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thoActorDo) Returning(value interface{}, columns ...string) IThoActorDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thoActorDo) Not(conds ...gen.Condition) IThoActorDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thoActorDo) Or(conds ...gen.Condition) IThoActorDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thoActorDo) Select(conds ...field.Expr) IThoActorDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thoActorDo) Where(conds ...gen.Condition) IThoActorDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thoActorDo) Order(conds ...field.Expr) IThoActorDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thoActorDo) Distinct(cols ...field.Expr) IThoActorDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thoActorDo) Omit(cols ...field.Expr) IThoActorDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thoActorDo) Join(table schema.Tabler, on ...field.Expr) IThoActorDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thoActorDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThoActorDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thoActorDo) RightJoin(table schema.Tabler, on ...field.Expr) IThoActorDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thoActorDo) Group(cols ...field.Expr) IThoActorDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thoActorDo) Having(conds ...gen.Condition) IThoActorDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thoActorDo) Limit(limit int) IThoActorDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thoActorDo) Offset(offset int) IThoActorDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thoActorDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThoActorDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thoActorDo) Unscoped() IThoActorDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thoActorDo) Create(values ...*model.ThoActor) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thoActorDo) CreateInBatches(values []*model.ThoActor, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thoActorDo) Save(values ...*model.ThoActor) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thoActorDo) First() (*model.ThoActor, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoActor), nil
	}
}

func (t thoActorDo) Take() (*model.ThoActor, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoActor), nil
	}
}

func (t thoActorDo) Last() (*model.ThoActor, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoActor), nil
	}
}

func (t thoActorDo) Find() ([]*model.ThoActor, error) {
	result, err := t.DO.Find()
	return result.([]*model.ThoActor), err
}

func (t thoActorDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoActor, err error) {
	buf := make([]*model.ThoActor, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thoActorDo) FindInBatches(result *[]*model.ThoActor, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thoActorDo) Attrs(attrs ...field.AssignExpr) IThoActorDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thoActorDo) Assign(attrs ...field.AssignExpr) IThoActorDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thoActorDo) Joins(fields ...field.RelationField) IThoActorDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thoActorDo) Preload(fields ...field.RelationField) IThoActorDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thoActorDo) FirstOrInit() (*model.ThoActor, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoActor), nil
	}
}

func (t thoActorDo) FirstOrCreate() (*model.ThoActor, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoActor), nil
	}
}

func (t thoActorDo) FindByPage(offset int, limit int) (result []*model.ThoActor, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thoActorDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thoActorDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thoActorDo) Delete(models ...*model.ThoActor) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thoActorDo) withDO(do gen.Dao) *thoActorDo {
	t.DO = *do.(*gen.DO)
	return t
}
