// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/wilsonce/rabbitmv_bot/dao/model"
)

func newThoRole(db *gorm.DB, opts ...gen.DOOption) thoRole {
	_thoRole := thoRole{}

	_thoRole.thoRoleDo.UseDB(db, opts...)
	_thoRole.thoRoleDo.UseModel(&model.ThoRole{})

	tableName := _thoRole.thoRoleDo.TableName()
	_thoRole.ALL = field.NewAsterisk(tableName)
	_thoRole.RoleID = field.NewInt32(tableName, "role_id")
	_thoRole.RoleRid = field.NewInt32(tableName, "role_rid")
	_thoRole.RoleName = field.NewString(tableName, "role_name")
	_thoRole.RoleEn = field.NewString(tableName, "role_en")
	_thoRole.RoleStatus = field.NewInt8(tableName, "role_status")
	_thoRole.RoleLock = field.NewInt8(tableName, "role_lock")
	_thoRole.RoleLetter = field.NewString(tableName, "role_letter")
	_thoRole.RoleColor = field.NewString(tableName, "role_color")
	_thoRole.RoleActor = field.NewString(tableName, "role_actor")
	_thoRole.RoleRemarks = field.NewString(tableName, "role_remarks")
	_thoRole.RolePic = field.NewString(tableName, "role_pic")
	_thoRole.RoleSort = field.NewInt32(tableName, "role_sort")
	_thoRole.RoleLevel = field.NewInt8(tableName, "role_level")
	_thoRole.RoleTime = field.NewInt32(tableName, "role_time")
	_thoRole.RoleTimeAdd = field.NewInt32(tableName, "role_time_add")
	_thoRole.RoleTimeHits = field.NewInt32(tableName, "role_time_hits")
	_thoRole.RoleTimeMake = field.NewInt32(tableName, "role_time_make")
	_thoRole.RoleHits = field.NewInt32(tableName, "role_hits")
	_thoRole.RoleHitsDay = field.NewInt32(tableName, "role_hits_day")
	_thoRole.RoleHitsWeek = field.NewInt32(tableName, "role_hits_week")
	_thoRole.RoleHitsMonth = field.NewInt32(tableName, "role_hits_month")
	_thoRole.RoleScore = field.NewFloat64(tableName, "role_score")
	_thoRole.RoleScoreAll = field.NewInt32(tableName, "role_score_all")
	_thoRole.RoleScoreNum = field.NewInt32(tableName, "role_score_num")
	_thoRole.RoleUp = field.NewInt32(tableName, "role_up")
	_thoRole.RoleDown = field.NewInt32(tableName, "role_down")
	_thoRole.RoleTpl = field.NewString(tableName, "role_tpl")
	_thoRole.RoleJumpurl = field.NewString(tableName, "role_jumpurl")
	_thoRole.RoleContent = field.NewString(tableName, "role_content")

	_thoRole.fillFieldMap()

	return _thoRole
}

type thoRole struct {
	thoRoleDo

	ALL           field.Asterisk
	RoleID        field.Int32
	RoleRid       field.Int32
	RoleName      field.String
	RoleEn        field.String
	RoleStatus    field.Int8
	RoleLock      field.Int8
	RoleLetter    field.String
	RoleColor     field.String
	RoleActor     field.String
	RoleRemarks   field.String
	RolePic       field.String
	RoleSort      field.Int32
	RoleLevel     field.Int8
	RoleTime      field.Int32
	RoleTimeAdd   field.Int32
	RoleTimeHits  field.Int32
	RoleTimeMake  field.Int32
	RoleHits      field.Int32
	RoleHitsDay   field.Int32
	RoleHitsWeek  field.Int32
	RoleHitsMonth field.Int32
	RoleScore     field.Float64
	RoleScoreAll  field.Int32
	RoleScoreNum  field.Int32
	RoleUp        field.Int32
	RoleDown      field.Int32
	RoleTpl       field.String
	RoleJumpurl   field.String
	RoleContent   field.String

	fieldMap map[string]field.Expr
}

func (t thoRole) Table(newTableName string) *thoRole {
	t.thoRoleDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thoRole) As(alias string) *thoRole {
	t.thoRoleDo.DO = *(t.thoRoleDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thoRole) updateTableName(table string) *thoRole {
	t.ALL = field.NewAsterisk(table)
	t.RoleID = field.NewInt32(table, "role_id")
	t.RoleRid = field.NewInt32(table, "role_rid")
	t.RoleName = field.NewString(table, "role_name")
	t.RoleEn = field.NewString(table, "role_en")
	t.RoleStatus = field.NewInt8(table, "role_status")
	t.RoleLock = field.NewInt8(table, "role_lock")
	t.RoleLetter = field.NewString(table, "role_letter")
	t.RoleColor = field.NewString(table, "role_color")
	t.RoleActor = field.NewString(table, "role_actor")
	t.RoleRemarks = field.NewString(table, "role_remarks")
	t.RolePic = field.NewString(table, "role_pic")
	t.RoleSort = field.NewInt32(table, "role_sort")
	t.RoleLevel = field.NewInt8(table, "role_level")
	t.RoleTime = field.NewInt32(table, "role_time")
	t.RoleTimeAdd = field.NewInt32(table, "role_time_add")
	t.RoleTimeHits = field.NewInt32(table, "role_time_hits")
	t.RoleTimeMake = field.NewInt32(table, "role_time_make")
	t.RoleHits = field.NewInt32(table, "role_hits")
	t.RoleHitsDay = field.NewInt32(table, "role_hits_day")
	t.RoleHitsWeek = field.NewInt32(table, "role_hits_week")
	t.RoleHitsMonth = field.NewInt32(table, "role_hits_month")
	t.RoleScore = field.NewFloat64(table, "role_score")
	t.RoleScoreAll = field.NewInt32(table, "role_score_all")
	t.RoleScoreNum = field.NewInt32(table, "role_score_num")
	t.RoleUp = field.NewInt32(table, "role_up")
	t.RoleDown = field.NewInt32(table, "role_down")
	t.RoleTpl = field.NewString(table, "role_tpl")
	t.RoleJumpurl = field.NewString(table, "role_jumpurl")
	t.RoleContent = field.NewString(table, "role_content")

	t.fillFieldMap()

	return t
}

func (t *thoRole) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thoRole) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 29)
	t.fieldMap["role_id"] = t.RoleID
	t.fieldMap["role_rid"] = t.RoleRid
	t.fieldMap["role_name"] = t.RoleName
	t.fieldMap["role_en"] = t.RoleEn
	t.fieldMap["role_status"] = t.RoleStatus
	t.fieldMap["role_lock"] = t.RoleLock
	t.fieldMap["role_letter"] = t.RoleLetter
	t.fieldMap["role_color"] = t.RoleColor
	t.fieldMap["role_actor"] = t.RoleActor
	t.fieldMap["role_remarks"] = t.RoleRemarks
	t.fieldMap["role_pic"] = t.RolePic
	t.fieldMap["role_sort"] = t.RoleSort
	t.fieldMap["role_level"] = t.RoleLevel
	t.fieldMap["role_time"] = t.RoleTime
	t.fieldMap["role_time_add"] = t.RoleTimeAdd
	t.fieldMap["role_time_hits"] = t.RoleTimeHits
	t.fieldMap["role_time_make"] = t.RoleTimeMake
	t.fieldMap["role_hits"] = t.RoleHits
	t.fieldMap["role_hits_day"] = t.RoleHitsDay
	t.fieldMap["role_hits_week"] = t.RoleHitsWeek
	t.fieldMap["role_hits_month"] = t.RoleHitsMonth
	t.fieldMap["role_score"] = t.RoleScore
	t.fieldMap["role_score_all"] = t.RoleScoreAll
	t.fieldMap["role_score_num"] = t.RoleScoreNum
	t.fieldMap["role_up"] = t.RoleUp
	t.fieldMap["role_down"] = t.RoleDown
	t.fieldMap["role_tpl"] = t.RoleTpl
	t.fieldMap["role_jumpurl"] = t.RoleJumpurl
	t.fieldMap["role_content"] = t.RoleContent
}

func (t thoRole) clone(db *gorm.DB) thoRole {
	t.thoRoleDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thoRole) replaceDB(db *gorm.DB) thoRole {
	t.thoRoleDo.ReplaceDB(db)
	return t
}

type thoRoleDo struct{ gen.DO }

type IThoRoleDo interface {
	gen.SubQuery
	Debug() IThoRoleDo
	WithContext(ctx context.Context) IThoRoleDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThoRoleDo
	WriteDB() IThoRoleDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThoRoleDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThoRoleDo
	Not(conds ...gen.Condition) IThoRoleDo
	Or(conds ...gen.Condition) IThoRoleDo
	Select(conds ...field.Expr) IThoRoleDo
	Where(conds ...gen.Condition) IThoRoleDo
	Order(conds ...field.Expr) IThoRoleDo
	Distinct(cols ...field.Expr) IThoRoleDo
	Omit(cols ...field.Expr) IThoRoleDo
	Join(table schema.Tabler, on ...field.Expr) IThoRoleDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThoRoleDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThoRoleDo
	Group(cols ...field.Expr) IThoRoleDo
	Having(conds ...gen.Condition) IThoRoleDo
	Limit(limit int) IThoRoleDo
	Offset(offset int) IThoRoleDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThoRoleDo
	Unscoped() IThoRoleDo
	Create(values ...*model.ThoRole) error
	CreateInBatches(values []*model.ThoRole, batchSize int) error
	Save(values ...*model.ThoRole) error
	First() (*model.ThoRole, error)
	Take() (*model.ThoRole, error)
	Last() (*model.ThoRole, error)
	Find() ([]*model.ThoRole, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoRole, err error)
	FindInBatches(result *[]*model.ThoRole, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThoRole) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThoRoleDo
	Assign(attrs ...field.AssignExpr) IThoRoleDo
	Joins(fields ...field.RelationField) IThoRoleDo
	Preload(fields ...field.RelationField) IThoRoleDo
	FirstOrInit() (*model.ThoRole, error)
	FirstOrCreate() (*model.ThoRole, error)
	FindByPage(offset int, limit int) (result []*model.ThoRole, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThoRoleDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thoRoleDo) Debug() IThoRoleDo {
	return t.withDO(t.DO.Debug())
}

func (t thoRoleDo) WithContext(ctx context.Context) IThoRoleDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thoRoleDo) ReadDB() IThoRoleDo {
	return t.Clauses(dbresolver.Read)
}

func (t thoRoleDo) WriteDB() IThoRoleDo {
	return t.Clauses(dbresolver.Write)
}

func (t thoRoleDo) Session(config *gorm.Session) IThoRoleDo {
	return t.withDO(t.DO.Session(config))
}

func (t thoRoleDo) Clauses(conds ...clause.Expression) IThoRoleDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thoRoleDo) Returning(value interface{}, columns ...string) IThoRoleDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thoRoleDo) Not(conds ...gen.Condition) IThoRoleDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thoRoleDo) Or(conds ...gen.Condition) IThoRoleDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thoRoleDo) Select(conds ...field.Expr) IThoRoleDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thoRoleDo) Where(conds ...gen.Condition) IThoRoleDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thoRoleDo) Order(conds ...field.Expr) IThoRoleDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thoRoleDo) Distinct(cols ...field.Expr) IThoRoleDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thoRoleDo) Omit(cols ...field.Expr) IThoRoleDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thoRoleDo) Join(table schema.Tabler, on ...field.Expr) IThoRoleDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thoRoleDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThoRoleDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thoRoleDo) RightJoin(table schema.Tabler, on ...field.Expr) IThoRoleDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thoRoleDo) Group(cols ...field.Expr) IThoRoleDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thoRoleDo) Having(conds ...gen.Condition) IThoRoleDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thoRoleDo) Limit(limit int) IThoRoleDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thoRoleDo) Offset(offset int) IThoRoleDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thoRoleDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThoRoleDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thoRoleDo) Unscoped() IThoRoleDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thoRoleDo) Create(values ...*model.ThoRole) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thoRoleDo) CreateInBatches(values []*model.ThoRole, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thoRoleDo) Save(values ...*model.ThoRole) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thoRoleDo) First() (*model.ThoRole, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoRole), nil
	}
}

func (t thoRoleDo) Take() (*model.ThoRole, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoRole), nil
	}
}

func (t thoRoleDo) Last() (*model.ThoRole, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoRole), nil
	}
}

func (t thoRoleDo) Find() ([]*model.ThoRole, error) {
	result, err := t.DO.Find()
	return result.([]*model.ThoRole), err
}

func (t thoRoleDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoRole, err error) {
	buf := make([]*model.ThoRole, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thoRoleDo) FindInBatches(result *[]*model.ThoRole, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thoRoleDo) Attrs(attrs ...field.AssignExpr) IThoRoleDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thoRoleDo) Assign(attrs ...field.AssignExpr) IThoRoleDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thoRoleDo) Joins(fields ...field.RelationField) IThoRoleDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thoRoleDo) Preload(fields ...field.RelationField) IThoRoleDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thoRoleDo) FirstOrInit() (*model.ThoRole, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoRole), nil
	}
}

func (t thoRoleDo) FirstOrCreate() (*model.ThoRole, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoRole), nil
	}
}

func (t thoRoleDo) FindByPage(offset int, limit int) (result []*model.ThoRole, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thoRoleDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thoRoleDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thoRoleDo) Delete(models ...*model.ThoRole) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thoRoleDo) withDO(do gen.Dao) *thoRoleDo {
	t.DO = *do.(*gen.DO)
	return t
}
