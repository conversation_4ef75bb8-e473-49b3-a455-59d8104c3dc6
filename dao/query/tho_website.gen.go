// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/wilsonce/rabbitmv_bot/dao/model"
)

func newThoWebsite(db *gorm.DB, opts ...gen.DOOption) thoWebsite {
	_thoWebsite := thoWebsite{}

	_thoWebsite.thoWebsiteDo.UseDB(db, opts...)
	_thoWebsite.thoWebsiteDo.UseModel(&model.ThoWebsite{})

	tableName := _thoWebsite.thoWebsiteDo.TableName()
	_thoWebsite.ALL = field.NewAsterisk(tableName)
	_thoWebsite.WebsiteID = field.NewInt32(tableName, "website_id")
	_thoWebsite.TypeID = field.NewInt32(tableName, "type_id")
	_thoWebsite.TypeId1 = field.NewInt32(tableName, "type_id_1")
	_thoWebsite.WebsiteName = field.NewString(tableName, "website_name")
	_thoWebsite.WebsiteSub = field.NewString(tableName, "website_sub")
	_thoWebsite.WebsiteEn = field.NewString(tableName, "website_en")
	_thoWebsite.WebsiteStatus = field.NewInt8(tableName, "website_status")
	_thoWebsite.WebsiteLetter = field.NewString(tableName, "website_letter")
	_thoWebsite.WebsiteColor = field.NewString(tableName, "website_color")
	_thoWebsite.WebsiteLock = field.NewInt8(tableName, "website_lock")
	_thoWebsite.WebsiteSort = field.NewInt32(tableName, "website_sort")
	_thoWebsite.WebsiteJumpurl = field.NewString(tableName, "website_jumpurl")
	_thoWebsite.WebsitePic = field.NewString(tableName, "website_pic")
	_thoWebsite.WebsitePicScreenshot = field.NewString(tableName, "website_pic_screenshot")
	_thoWebsite.WebsiteLogo = field.NewString(tableName, "website_logo")
	_thoWebsite.WebsiteArea = field.NewString(tableName, "website_area")
	_thoWebsite.WebsiteLang = field.NewString(tableName, "website_lang")
	_thoWebsite.WebsiteLevel = field.NewInt8(tableName, "website_level")
	_thoWebsite.WebsiteTime = field.NewInt32(tableName, "website_time")
	_thoWebsite.WebsiteTimeAdd = field.NewInt32(tableName, "website_time_add")
	_thoWebsite.WebsiteTimeHits = field.NewInt32(tableName, "website_time_hits")
	_thoWebsite.WebsiteTimeMake = field.NewInt32(tableName, "website_time_make")
	_thoWebsite.WebsiteTimeReferer = field.NewInt32(tableName, "website_time_referer")
	_thoWebsite.WebsiteHits = field.NewInt32(tableName, "website_hits")
	_thoWebsite.WebsiteHitsDay = field.NewInt32(tableName, "website_hits_day")
	_thoWebsite.WebsiteHitsWeek = field.NewInt32(tableName, "website_hits_week")
	_thoWebsite.WebsiteHitsMonth = field.NewInt32(tableName, "website_hits_month")
	_thoWebsite.WebsiteScore = field.NewFloat64(tableName, "website_score")
	_thoWebsite.WebsiteScoreAll = field.NewInt32(tableName, "website_score_all")
	_thoWebsite.WebsiteScoreNum = field.NewInt32(tableName, "website_score_num")
	_thoWebsite.WebsiteUp = field.NewInt32(tableName, "website_up")
	_thoWebsite.WebsiteDown = field.NewInt32(tableName, "website_down")
	_thoWebsite.WebsiteReferer = field.NewInt32(tableName, "website_referer")
	_thoWebsite.WebsiteRefererDay = field.NewInt32(tableName, "website_referer_day")
	_thoWebsite.WebsiteRefererWeek = field.NewInt32(tableName, "website_referer_week")
	_thoWebsite.WebsiteRefererMonth = field.NewInt32(tableName, "website_referer_month")
	_thoWebsite.WebsiteTag = field.NewString(tableName, "website_tag")
	_thoWebsite.WebsiteClass = field.NewString(tableName, "website_class")
	_thoWebsite.WebsiteRemarks = field.NewString(tableName, "website_remarks")
	_thoWebsite.WebsiteTpl = field.NewString(tableName, "website_tpl")
	_thoWebsite.WebsiteBlurb = field.NewString(tableName, "website_blurb")
	_thoWebsite.WebsiteContent = field.NewString(tableName, "website_content")

	_thoWebsite.fillFieldMap()

	return _thoWebsite
}

type thoWebsite struct {
	thoWebsiteDo

	ALL                  field.Asterisk
	WebsiteID            field.Int32
	TypeID               field.Int32
	TypeId1              field.Int32
	WebsiteName          field.String
	WebsiteSub           field.String
	WebsiteEn            field.String
	WebsiteStatus        field.Int8
	WebsiteLetter        field.String
	WebsiteColor         field.String
	WebsiteLock          field.Int8
	WebsiteSort          field.Int32
	WebsiteJumpurl       field.String
	WebsitePic           field.String
	WebsitePicScreenshot field.String
	WebsiteLogo          field.String
	WebsiteArea          field.String
	WebsiteLang          field.String
	WebsiteLevel         field.Int8
	WebsiteTime          field.Int32
	WebsiteTimeAdd       field.Int32
	WebsiteTimeHits      field.Int32
	WebsiteTimeMake      field.Int32
	WebsiteTimeReferer   field.Int32
	WebsiteHits          field.Int32
	WebsiteHitsDay       field.Int32
	WebsiteHitsWeek      field.Int32
	WebsiteHitsMonth     field.Int32
	WebsiteScore         field.Float64
	WebsiteScoreAll      field.Int32
	WebsiteScoreNum      field.Int32
	WebsiteUp            field.Int32
	WebsiteDown          field.Int32
	WebsiteReferer       field.Int32
	WebsiteRefererDay    field.Int32
	WebsiteRefererWeek   field.Int32
	WebsiteRefererMonth  field.Int32
	WebsiteTag           field.String
	WebsiteClass         field.String
	WebsiteRemarks       field.String
	WebsiteTpl           field.String
	WebsiteBlurb         field.String
	WebsiteContent       field.String

	fieldMap map[string]field.Expr
}

func (t thoWebsite) Table(newTableName string) *thoWebsite {
	t.thoWebsiteDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thoWebsite) As(alias string) *thoWebsite {
	t.thoWebsiteDo.DO = *(t.thoWebsiteDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thoWebsite) updateTableName(table string) *thoWebsite {
	t.ALL = field.NewAsterisk(table)
	t.WebsiteID = field.NewInt32(table, "website_id")
	t.TypeID = field.NewInt32(table, "type_id")
	t.TypeId1 = field.NewInt32(table, "type_id_1")
	t.WebsiteName = field.NewString(table, "website_name")
	t.WebsiteSub = field.NewString(table, "website_sub")
	t.WebsiteEn = field.NewString(table, "website_en")
	t.WebsiteStatus = field.NewInt8(table, "website_status")
	t.WebsiteLetter = field.NewString(table, "website_letter")
	t.WebsiteColor = field.NewString(table, "website_color")
	t.WebsiteLock = field.NewInt8(table, "website_lock")
	t.WebsiteSort = field.NewInt32(table, "website_sort")
	t.WebsiteJumpurl = field.NewString(table, "website_jumpurl")
	t.WebsitePic = field.NewString(table, "website_pic")
	t.WebsitePicScreenshot = field.NewString(table, "website_pic_screenshot")
	t.WebsiteLogo = field.NewString(table, "website_logo")
	t.WebsiteArea = field.NewString(table, "website_area")
	t.WebsiteLang = field.NewString(table, "website_lang")
	t.WebsiteLevel = field.NewInt8(table, "website_level")
	t.WebsiteTime = field.NewInt32(table, "website_time")
	t.WebsiteTimeAdd = field.NewInt32(table, "website_time_add")
	t.WebsiteTimeHits = field.NewInt32(table, "website_time_hits")
	t.WebsiteTimeMake = field.NewInt32(table, "website_time_make")
	t.WebsiteTimeReferer = field.NewInt32(table, "website_time_referer")
	t.WebsiteHits = field.NewInt32(table, "website_hits")
	t.WebsiteHitsDay = field.NewInt32(table, "website_hits_day")
	t.WebsiteHitsWeek = field.NewInt32(table, "website_hits_week")
	t.WebsiteHitsMonth = field.NewInt32(table, "website_hits_month")
	t.WebsiteScore = field.NewFloat64(table, "website_score")
	t.WebsiteScoreAll = field.NewInt32(table, "website_score_all")
	t.WebsiteScoreNum = field.NewInt32(table, "website_score_num")
	t.WebsiteUp = field.NewInt32(table, "website_up")
	t.WebsiteDown = field.NewInt32(table, "website_down")
	t.WebsiteReferer = field.NewInt32(table, "website_referer")
	t.WebsiteRefererDay = field.NewInt32(table, "website_referer_day")
	t.WebsiteRefererWeek = field.NewInt32(table, "website_referer_week")
	t.WebsiteRefererMonth = field.NewInt32(table, "website_referer_month")
	t.WebsiteTag = field.NewString(table, "website_tag")
	t.WebsiteClass = field.NewString(table, "website_class")
	t.WebsiteRemarks = field.NewString(table, "website_remarks")
	t.WebsiteTpl = field.NewString(table, "website_tpl")
	t.WebsiteBlurb = field.NewString(table, "website_blurb")
	t.WebsiteContent = field.NewString(table, "website_content")

	t.fillFieldMap()

	return t
}

func (t *thoWebsite) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thoWebsite) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 42)
	t.fieldMap["website_id"] = t.WebsiteID
	t.fieldMap["type_id"] = t.TypeID
	t.fieldMap["type_id_1"] = t.TypeId1
	t.fieldMap["website_name"] = t.WebsiteName
	t.fieldMap["website_sub"] = t.WebsiteSub
	t.fieldMap["website_en"] = t.WebsiteEn
	t.fieldMap["website_status"] = t.WebsiteStatus
	t.fieldMap["website_letter"] = t.WebsiteLetter
	t.fieldMap["website_color"] = t.WebsiteColor
	t.fieldMap["website_lock"] = t.WebsiteLock
	t.fieldMap["website_sort"] = t.WebsiteSort
	t.fieldMap["website_jumpurl"] = t.WebsiteJumpurl
	t.fieldMap["website_pic"] = t.WebsitePic
	t.fieldMap["website_pic_screenshot"] = t.WebsitePicScreenshot
	t.fieldMap["website_logo"] = t.WebsiteLogo
	t.fieldMap["website_area"] = t.WebsiteArea
	t.fieldMap["website_lang"] = t.WebsiteLang
	t.fieldMap["website_level"] = t.WebsiteLevel
	t.fieldMap["website_time"] = t.WebsiteTime
	t.fieldMap["website_time_add"] = t.WebsiteTimeAdd
	t.fieldMap["website_time_hits"] = t.WebsiteTimeHits
	t.fieldMap["website_time_make"] = t.WebsiteTimeMake
	t.fieldMap["website_time_referer"] = t.WebsiteTimeReferer
	t.fieldMap["website_hits"] = t.WebsiteHits
	t.fieldMap["website_hits_day"] = t.WebsiteHitsDay
	t.fieldMap["website_hits_week"] = t.WebsiteHitsWeek
	t.fieldMap["website_hits_month"] = t.WebsiteHitsMonth
	t.fieldMap["website_score"] = t.WebsiteScore
	t.fieldMap["website_score_all"] = t.WebsiteScoreAll
	t.fieldMap["website_score_num"] = t.WebsiteScoreNum
	t.fieldMap["website_up"] = t.WebsiteUp
	t.fieldMap["website_down"] = t.WebsiteDown
	t.fieldMap["website_referer"] = t.WebsiteReferer
	t.fieldMap["website_referer_day"] = t.WebsiteRefererDay
	t.fieldMap["website_referer_week"] = t.WebsiteRefererWeek
	t.fieldMap["website_referer_month"] = t.WebsiteRefererMonth
	t.fieldMap["website_tag"] = t.WebsiteTag
	t.fieldMap["website_class"] = t.WebsiteClass
	t.fieldMap["website_remarks"] = t.WebsiteRemarks
	t.fieldMap["website_tpl"] = t.WebsiteTpl
	t.fieldMap["website_blurb"] = t.WebsiteBlurb
	t.fieldMap["website_content"] = t.WebsiteContent
}

func (t thoWebsite) clone(db *gorm.DB) thoWebsite {
	t.thoWebsiteDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thoWebsite) replaceDB(db *gorm.DB) thoWebsite {
	t.thoWebsiteDo.ReplaceDB(db)
	return t
}

type thoWebsiteDo struct{ gen.DO }

type IThoWebsiteDo interface {
	gen.SubQuery
	Debug() IThoWebsiteDo
	WithContext(ctx context.Context) IThoWebsiteDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThoWebsiteDo
	WriteDB() IThoWebsiteDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThoWebsiteDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThoWebsiteDo
	Not(conds ...gen.Condition) IThoWebsiteDo
	Or(conds ...gen.Condition) IThoWebsiteDo
	Select(conds ...field.Expr) IThoWebsiteDo
	Where(conds ...gen.Condition) IThoWebsiteDo
	Order(conds ...field.Expr) IThoWebsiteDo
	Distinct(cols ...field.Expr) IThoWebsiteDo
	Omit(cols ...field.Expr) IThoWebsiteDo
	Join(table schema.Tabler, on ...field.Expr) IThoWebsiteDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThoWebsiteDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThoWebsiteDo
	Group(cols ...field.Expr) IThoWebsiteDo
	Having(conds ...gen.Condition) IThoWebsiteDo
	Limit(limit int) IThoWebsiteDo
	Offset(offset int) IThoWebsiteDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThoWebsiteDo
	Unscoped() IThoWebsiteDo
	Create(values ...*model.ThoWebsite) error
	CreateInBatches(values []*model.ThoWebsite, batchSize int) error
	Save(values ...*model.ThoWebsite) error
	First() (*model.ThoWebsite, error)
	Take() (*model.ThoWebsite, error)
	Last() (*model.ThoWebsite, error)
	Find() ([]*model.ThoWebsite, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoWebsite, err error)
	FindInBatches(result *[]*model.ThoWebsite, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThoWebsite) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThoWebsiteDo
	Assign(attrs ...field.AssignExpr) IThoWebsiteDo
	Joins(fields ...field.RelationField) IThoWebsiteDo
	Preload(fields ...field.RelationField) IThoWebsiteDo
	FirstOrInit() (*model.ThoWebsite, error)
	FirstOrCreate() (*model.ThoWebsite, error)
	FindByPage(offset int, limit int) (result []*model.ThoWebsite, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThoWebsiteDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thoWebsiteDo) Debug() IThoWebsiteDo {
	return t.withDO(t.DO.Debug())
}

func (t thoWebsiteDo) WithContext(ctx context.Context) IThoWebsiteDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thoWebsiteDo) ReadDB() IThoWebsiteDo {
	return t.Clauses(dbresolver.Read)
}

func (t thoWebsiteDo) WriteDB() IThoWebsiteDo {
	return t.Clauses(dbresolver.Write)
}

func (t thoWebsiteDo) Session(config *gorm.Session) IThoWebsiteDo {
	return t.withDO(t.DO.Session(config))
}

func (t thoWebsiteDo) Clauses(conds ...clause.Expression) IThoWebsiteDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thoWebsiteDo) Returning(value interface{}, columns ...string) IThoWebsiteDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thoWebsiteDo) Not(conds ...gen.Condition) IThoWebsiteDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thoWebsiteDo) Or(conds ...gen.Condition) IThoWebsiteDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thoWebsiteDo) Select(conds ...field.Expr) IThoWebsiteDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thoWebsiteDo) Where(conds ...gen.Condition) IThoWebsiteDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thoWebsiteDo) Order(conds ...field.Expr) IThoWebsiteDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thoWebsiteDo) Distinct(cols ...field.Expr) IThoWebsiteDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thoWebsiteDo) Omit(cols ...field.Expr) IThoWebsiteDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thoWebsiteDo) Join(table schema.Tabler, on ...field.Expr) IThoWebsiteDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thoWebsiteDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThoWebsiteDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thoWebsiteDo) RightJoin(table schema.Tabler, on ...field.Expr) IThoWebsiteDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thoWebsiteDo) Group(cols ...field.Expr) IThoWebsiteDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thoWebsiteDo) Having(conds ...gen.Condition) IThoWebsiteDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thoWebsiteDo) Limit(limit int) IThoWebsiteDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thoWebsiteDo) Offset(offset int) IThoWebsiteDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thoWebsiteDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThoWebsiteDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thoWebsiteDo) Unscoped() IThoWebsiteDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thoWebsiteDo) Create(values ...*model.ThoWebsite) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thoWebsiteDo) CreateInBatches(values []*model.ThoWebsite, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thoWebsiteDo) Save(values ...*model.ThoWebsite) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thoWebsiteDo) First() (*model.ThoWebsite, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoWebsite), nil
	}
}

func (t thoWebsiteDo) Take() (*model.ThoWebsite, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoWebsite), nil
	}
}

func (t thoWebsiteDo) Last() (*model.ThoWebsite, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoWebsite), nil
	}
}

func (t thoWebsiteDo) Find() ([]*model.ThoWebsite, error) {
	result, err := t.DO.Find()
	return result.([]*model.ThoWebsite), err
}

func (t thoWebsiteDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoWebsite, err error) {
	buf := make([]*model.ThoWebsite, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thoWebsiteDo) FindInBatches(result *[]*model.ThoWebsite, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thoWebsiteDo) Attrs(attrs ...field.AssignExpr) IThoWebsiteDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thoWebsiteDo) Assign(attrs ...field.AssignExpr) IThoWebsiteDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thoWebsiteDo) Joins(fields ...field.RelationField) IThoWebsiteDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thoWebsiteDo) Preload(fields ...field.RelationField) IThoWebsiteDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thoWebsiteDo) FirstOrInit() (*model.ThoWebsite, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoWebsite), nil
	}
}

func (t thoWebsiteDo) FirstOrCreate() (*model.ThoWebsite, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoWebsite), nil
	}
}

func (t thoWebsiteDo) FindByPage(offset int, limit int) (result []*model.ThoWebsite, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thoWebsiteDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thoWebsiteDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thoWebsiteDo) Delete(models ...*model.ThoWebsite) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thoWebsiteDo) withDO(do gen.Dao) *thoWebsiteDo {
	t.DO = *do.(*gen.DO)
	return t
}
