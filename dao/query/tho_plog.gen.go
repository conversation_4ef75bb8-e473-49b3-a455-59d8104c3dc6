// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/wilsonce/rabbitmv_bot/dao/model"
)

func newThoPlog(db *gorm.DB, opts ...gen.DOOption) thoPlog {
	_thoPlog := thoPlog{}

	_thoPlog.thoPlogDo.UseDB(db, opts...)
	_thoPlog.thoPlogDo.UseModel(&model.ThoPlog{})

	tableName := _thoPlog.thoPlogDo.TableName()
	_thoPlog.ALL = field.NewAsterisk(tableName)
	_thoPlog.PlogID = field.NewInt32(tableName, "plog_id")
	_thoPlog.UserID = field.NewInt32(tableName, "user_id")
	_thoPlog.UserId1 = field.NewInt32(tableName, "user_id_1")
	_thoPlog.PlogType = field.NewInt8(tableName, "plog_type")
	_thoPlog.PlogPoints = field.NewInt32(tableName, "plog_points")
	_thoPlog.PlogTime = field.NewInt32(tableName, "plog_time")
	_thoPlog.PlogRemarks = field.NewString(tableName, "plog_remarks")

	_thoPlog.fillFieldMap()

	return _thoPlog
}

type thoPlog struct {
	thoPlogDo

	ALL         field.Asterisk
	PlogID      field.Int32
	UserID      field.Int32
	UserId1     field.Int32
	PlogType    field.Int8
	PlogPoints  field.Int32
	PlogTime    field.Int32
	PlogRemarks field.String

	fieldMap map[string]field.Expr
}

func (t thoPlog) Table(newTableName string) *thoPlog {
	t.thoPlogDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thoPlog) As(alias string) *thoPlog {
	t.thoPlogDo.DO = *(t.thoPlogDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thoPlog) updateTableName(table string) *thoPlog {
	t.ALL = field.NewAsterisk(table)
	t.PlogID = field.NewInt32(table, "plog_id")
	t.UserID = field.NewInt32(table, "user_id")
	t.UserId1 = field.NewInt32(table, "user_id_1")
	t.PlogType = field.NewInt8(table, "plog_type")
	t.PlogPoints = field.NewInt32(table, "plog_points")
	t.PlogTime = field.NewInt32(table, "plog_time")
	t.PlogRemarks = field.NewString(table, "plog_remarks")

	t.fillFieldMap()

	return t
}

func (t *thoPlog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thoPlog) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 7)
	t.fieldMap["plog_id"] = t.PlogID
	t.fieldMap["user_id"] = t.UserID
	t.fieldMap["user_id_1"] = t.UserId1
	t.fieldMap["plog_type"] = t.PlogType
	t.fieldMap["plog_points"] = t.PlogPoints
	t.fieldMap["plog_time"] = t.PlogTime
	t.fieldMap["plog_remarks"] = t.PlogRemarks
}

func (t thoPlog) clone(db *gorm.DB) thoPlog {
	t.thoPlogDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thoPlog) replaceDB(db *gorm.DB) thoPlog {
	t.thoPlogDo.ReplaceDB(db)
	return t
}

type thoPlogDo struct{ gen.DO }

type IThoPlogDo interface {
	gen.SubQuery
	Debug() IThoPlogDo
	WithContext(ctx context.Context) IThoPlogDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThoPlogDo
	WriteDB() IThoPlogDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThoPlogDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThoPlogDo
	Not(conds ...gen.Condition) IThoPlogDo
	Or(conds ...gen.Condition) IThoPlogDo
	Select(conds ...field.Expr) IThoPlogDo
	Where(conds ...gen.Condition) IThoPlogDo
	Order(conds ...field.Expr) IThoPlogDo
	Distinct(cols ...field.Expr) IThoPlogDo
	Omit(cols ...field.Expr) IThoPlogDo
	Join(table schema.Tabler, on ...field.Expr) IThoPlogDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThoPlogDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThoPlogDo
	Group(cols ...field.Expr) IThoPlogDo
	Having(conds ...gen.Condition) IThoPlogDo
	Limit(limit int) IThoPlogDo
	Offset(offset int) IThoPlogDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThoPlogDo
	Unscoped() IThoPlogDo
	Create(values ...*model.ThoPlog) error
	CreateInBatches(values []*model.ThoPlog, batchSize int) error
	Save(values ...*model.ThoPlog) error
	First() (*model.ThoPlog, error)
	Take() (*model.ThoPlog, error)
	Last() (*model.ThoPlog, error)
	Find() ([]*model.ThoPlog, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoPlog, err error)
	FindInBatches(result *[]*model.ThoPlog, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThoPlog) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThoPlogDo
	Assign(attrs ...field.AssignExpr) IThoPlogDo
	Joins(fields ...field.RelationField) IThoPlogDo
	Preload(fields ...field.RelationField) IThoPlogDo
	FirstOrInit() (*model.ThoPlog, error)
	FirstOrCreate() (*model.ThoPlog, error)
	FindByPage(offset int, limit int) (result []*model.ThoPlog, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThoPlogDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thoPlogDo) Debug() IThoPlogDo {
	return t.withDO(t.DO.Debug())
}

func (t thoPlogDo) WithContext(ctx context.Context) IThoPlogDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thoPlogDo) ReadDB() IThoPlogDo {
	return t.Clauses(dbresolver.Read)
}

func (t thoPlogDo) WriteDB() IThoPlogDo {
	return t.Clauses(dbresolver.Write)
}

func (t thoPlogDo) Session(config *gorm.Session) IThoPlogDo {
	return t.withDO(t.DO.Session(config))
}

func (t thoPlogDo) Clauses(conds ...clause.Expression) IThoPlogDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thoPlogDo) Returning(value interface{}, columns ...string) IThoPlogDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thoPlogDo) Not(conds ...gen.Condition) IThoPlogDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thoPlogDo) Or(conds ...gen.Condition) IThoPlogDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thoPlogDo) Select(conds ...field.Expr) IThoPlogDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thoPlogDo) Where(conds ...gen.Condition) IThoPlogDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thoPlogDo) Order(conds ...field.Expr) IThoPlogDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thoPlogDo) Distinct(cols ...field.Expr) IThoPlogDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thoPlogDo) Omit(cols ...field.Expr) IThoPlogDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thoPlogDo) Join(table schema.Tabler, on ...field.Expr) IThoPlogDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thoPlogDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThoPlogDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thoPlogDo) RightJoin(table schema.Tabler, on ...field.Expr) IThoPlogDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thoPlogDo) Group(cols ...field.Expr) IThoPlogDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thoPlogDo) Having(conds ...gen.Condition) IThoPlogDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thoPlogDo) Limit(limit int) IThoPlogDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thoPlogDo) Offset(offset int) IThoPlogDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thoPlogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThoPlogDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thoPlogDo) Unscoped() IThoPlogDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thoPlogDo) Create(values ...*model.ThoPlog) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thoPlogDo) CreateInBatches(values []*model.ThoPlog, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thoPlogDo) Save(values ...*model.ThoPlog) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thoPlogDo) First() (*model.ThoPlog, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoPlog), nil
	}
}

func (t thoPlogDo) Take() (*model.ThoPlog, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoPlog), nil
	}
}

func (t thoPlogDo) Last() (*model.ThoPlog, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoPlog), nil
	}
}

func (t thoPlogDo) Find() ([]*model.ThoPlog, error) {
	result, err := t.DO.Find()
	return result.([]*model.ThoPlog), err
}

func (t thoPlogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoPlog, err error) {
	buf := make([]*model.ThoPlog, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thoPlogDo) FindInBatches(result *[]*model.ThoPlog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thoPlogDo) Attrs(attrs ...field.AssignExpr) IThoPlogDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thoPlogDo) Assign(attrs ...field.AssignExpr) IThoPlogDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thoPlogDo) Joins(fields ...field.RelationField) IThoPlogDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thoPlogDo) Preload(fields ...field.RelationField) IThoPlogDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thoPlogDo) FirstOrInit() (*model.ThoPlog, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoPlog), nil
	}
}

func (t thoPlogDo) FirstOrCreate() (*model.ThoPlog, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoPlog), nil
	}
}

func (t thoPlogDo) FindByPage(offset int, limit int) (result []*model.ThoPlog, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thoPlogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thoPlogDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thoPlogDo) Delete(models ...*model.ThoPlog) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thoPlogDo) withDO(do gen.Dao) *thoPlogDo {
	t.DO = *do.(*gen.DO)
	return t
}
