// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/wilsonce/rabbitmv_bot/dao/model"
)

func newThoComment(db *gorm.DB, opts ...gen.DOOption) thoComment {
	_thoComment := thoComment{}

	_thoComment.thoCommentDo.UseDB(db, opts...)
	_thoComment.thoCommentDo.UseModel(&model.ThoComment{})

	tableName := _thoComment.thoCommentDo.TableName()
	_thoComment.ALL = field.NewAsterisk(tableName)
	_thoComment.CommentID = field.NewInt32(tableName, "comment_id")
	_thoComment.CommentMid = field.NewInt8(tableName, "comment_mid")
	_thoComment.CommentRid = field.NewInt32(tableName, "comment_rid")
	_thoComment.CommentPid = field.NewInt32(tableName, "comment_pid")
	_thoComment.UserID = field.NewInt32(tableName, "user_id")
	_thoComment.CommentStatus = field.NewInt8(tableName, "comment_status")
	_thoComment.CommentName = field.NewString(tableName, "comment_name")
	_thoComment.CommentIP = field.NewInt32(tableName, "comment_ip")
	_thoComment.CommentTime = field.NewInt32(tableName, "comment_time")
	_thoComment.CommentContent = field.NewString(tableName, "comment_content")
	_thoComment.CommentUp = field.NewInt32(tableName, "comment_up")
	_thoComment.CommentDown = field.NewInt32(tableName, "comment_down")
	_thoComment.CommentReply = field.NewInt32(tableName, "comment_reply")
	_thoComment.CommentReport = field.NewInt32(tableName, "comment_report")

	_thoComment.fillFieldMap()

	return _thoComment
}

type thoComment struct {
	thoCommentDo

	ALL            field.Asterisk
	CommentID      field.Int32
	CommentMid     field.Int8
	CommentRid     field.Int32
	CommentPid     field.Int32
	UserID         field.Int32
	CommentStatus  field.Int8
	CommentName    field.String
	CommentIP      field.Int32
	CommentTime    field.Int32
	CommentContent field.String
	CommentUp      field.Int32
	CommentDown    field.Int32
	CommentReply   field.Int32
	CommentReport  field.Int32

	fieldMap map[string]field.Expr
}

func (t thoComment) Table(newTableName string) *thoComment {
	t.thoCommentDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thoComment) As(alias string) *thoComment {
	t.thoCommentDo.DO = *(t.thoCommentDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thoComment) updateTableName(table string) *thoComment {
	t.ALL = field.NewAsterisk(table)
	t.CommentID = field.NewInt32(table, "comment_id")
	t.CommentMid = field.NewInt8(table, "comment_mid")
	t.CommentRid = field.NewInt32(table, "comment_rid")
	t.CommentPid = field.NewInt32(table, "comment_pid")
	t.UserID = field.NewInt32(table, "user_id")
	t.CommentStatus = field.NewInt8(table, "comment_status")
	t.CommentName = field.NewString(table, "comment_name")
	t.CommentIP = field.NewInt32(table, "comment_ip")
	t.CommentTime = field.NewInt32(table, "comment_time")
	t.CommentContent = field.NewString(table, "comment_content")
	t.CommentUp = field.NewInt32(table, "comment_up")
	t.CommentDown = field.NewInt32(table, "comment_down")
	t.CommentReply = field.NewInt32(table, "comment_reply")
	t.CommentReport = field.NewInt32(table, "comment_report")

	t.fillFieldMap()

	return t
}

func (t *thoComment) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thoComment) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 14)
	t.fieldMap["comment_id"] = t.CommentID
	t.fieldMap["comment_mid"] = t.CommentMid
	t.fieldMap["comment_rid"] = t.CommentRid
	t.fieldMap["comment_pid"] = t.CommentPid
	t.fieldMap["user_id"] = t.UserID
	t.fieldMap["comment_status"] = t.CommentStatus
	t.fieldMap["comment_name"] = t.CommentName
	t.fieldMap["comment_ip"] = t.CommentIP
	t.fieldMap["comment_time"] = t.CommentTime
	t.fieldMap["comment_content"] = t.CommentContent
	t.fieldMap["comment_up"] = t.CommentUp
	t.fieldMap["comment_down"] = t.CommentDown
	t.fieldMap["comment_reply"] = t.CommentReply
	t.fieldMap["comment_report"] = t.CommentReport
}

func (t thoComment) clone(db *gorm.DB) thoComment {
	t.thoCommentDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thoComment) replaceDB(db *gorm.DB) thoComment {
	t.thoCommentDo.ReplaceDB(db)
	return t
}

type thoCommentDo struct{ gen.DO }

type IThoCommentDo interface {
	gen.SubQuery
	Debug() IThoCommentDo
	WithContext(ctx context.Context) IThoCommentDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThoCommentDo
	WriteDB() IThoCommentDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThoCommentDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThoCommentDo
	Not(conds ...gen.Condition) IThoCommentDo
	Or(conds ...gen.Condition) IThoCommentDo
	Select(conds ...field.Expr) IThoCommentDo
	Where(conds ...gen.Condition) IThoCommentDo
	Order(conds ...field.Expr) IThoCommentDo
	Distinct(cols ...field.Expr) IThoCommentDo
	Omit(cols ...field.Expr) IThoCommentDo
	Join(table schema.Tabler, on ...field.Expr) IThoCommentDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThoCommentDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThoCommentDo
	Group(cols ...field.Expr) IThoCommentDo
	Having(conds ...gen.Condition) IThoCommentDo
	Limit(limit int) IThoCommentDo
	Offset(offset int) IThoCommentDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThoCommentDo
	Unscoped() IThoCommentDo
	Create(values ...*model.ThoComment) error
	CreateInBatches(values []*model.ThoComment, batchSize int) error
	Save(values ...*model.ThoComment) error
	First() (*model.ThoComment, error)
	Take() (*model.ThoComment, error)
	Last() (*model.ThoComment, error)
	Find() ([]*model.ThoComment, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoComment, err error)
	FindInBatches(result *[]*model.ThoComment, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThoComment) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThoCommentDo
	Assign(attrs ...field.AssignExpr) IThoCommentDo
	Joins(fields ...field.RelationField) IThoCommentDo
	Preload(fields ...field.RelationField) IThoCommentDo
	FirstOrInit() (*model.ThoComment, error)
	FirstOrCreate() (*model.ThoComment, error)
	FindByPage(offset int, limit int) (result []*model.ThoComment, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThoCommentDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thoCommentDo) Debug() IThoCommentDo {
	return t.withDO(t.DO.Debug())
}

func (t thoCommentDo) WithContext(ctx context.Context) IThoCommentDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thoCommentDo) ReadDB() IThoCommentDo {
	return t.Clauses(dbresolver.Read)
}

func (t thoCommentDo) WriteDB() IThoCommentDo {
	return t.Clauses(dbresolver.Write)
}

func (t thoCommentDo) Session(config *gorm.Session) IThoCommentDo {
	return t.withDO(t.DO.Session(config))
}

func (t thoCommentDo) Clauses(conds ...clause.Expression) IThoCommentDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thoCommentDo) Returning(value interface{}, columns ...string) IThoCommentDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thoCommentDo) Not(conds ...gen.Condition) IThoCommentDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thoCommentDo) Or(conds ...gen.Condition) IThoCommentDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thoCommentDo) Select(conds ...field.Expr) IThoCommentDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thoCommentDo) Where(conds ...gen.Condition) IThoCommentDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thoCommentDo) Order(conds ...field.Expr) IThoCommentDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thoCommentDo) Distinct(cols ...field.Expr) IThoCommentDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thoCommentDo) Omit(cols ...field.Expr) IThoCommentDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thoCommentDo) Join(table schema.Tabler, on ...field.Expr) IThoCommentDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thoCommentDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThoCommentDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thoCommentDo) RightJoin(table schema.Tabler, on ...field.Expr) IThoCommentDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thoCommentDo) Group(cols ...field.Expr) IThoCommentDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thoCommentDo) Having(conds ...gen.Condition) IThoCommentDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thoCommentDo) Limit(limit int) IThoCommentDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thoCommentDo) Offset(offset int) IThoCommentDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thoCommentDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThoCommentDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thoCommentDo) Unscoped() IThoCommentDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thoCommentDo) Create(values ...*model.ThoComment) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thoCommentDo) CreateInBatches(values []*model.ThoComment, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thoCommentDo) Save(values ...*model.ThoComment) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thoCommentDo) First() (*model.ThoComment, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoComment), nil
	}
}

func (t thoCommentDo) Take() (*model.ThoComment, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoComment), nil
	}
}

func (t thoCommentDo) Last() (*model.ThoComment, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoComment), nil
	}
}

func (t thoCommentDo) Find() ([]*model.ThoComment, error) {
	result, err := t.DO.Find()
	return result.([]*model.ThoComment), err
}

func (t thoCommentDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoComment, err error) {
	buf := make([]*model.ThoComment, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thoCommentDo) FindInBatches(result *[]*model.ThoComment, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thoCommentDo) Attrs(attrs ...field.AssignExpr) IThoCommentDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thoCommentDo) Assign(attrs ...field.AssignExpr) IThoCommentDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thoCommentDo) Joins(fields ...field.RelationField) IThoCommentDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thoCommentDo) Preload(fields ...field.RelationField) IThoCommentDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thoCommentDo) FirstOrInit() (*model.ThoComment, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoComment), nil
	}
}

func (t thoCommentDo) FirstOrCreate() (*model.ThoComment, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoComment), nil
	}
}

func (t thoCommentDo) FindByPage(offset int, limit int) (result []*model.ThoComment, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thoCommentDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thoCommentDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thoCommentDo) Delete(models ...*model.ThoComment) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thoCommentDo) withDO(do gen.Dao) *thoCommentDo {
	t.DO = *do.(*gen.DO)
	return t
}
