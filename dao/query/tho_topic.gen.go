// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/wilsonce/rabbitmv_bot/dao/model"
)

func newThoTopic(db *gorm.DB, opts ...gen.DOOption) thoTopic {
	_thoTopic := thoTopic{}

	_thoTopic.thoTopicDo.UseDB(db, opts...)
	_thoTopic.thoTopicDo.UseModel(&model.ThoTopic{})

	tableName := _thoTopic.thoTopicDo.TableName()
	_thoTopic.ALL = field.NewAsterisk(tableName)
	_thoTopic.TopicID = field.NewInt32(tableName, "topic_id")
	_thoTopic.TopicName = field.NewString(tableName, "topic_name")
	_thoTopic.TopicEn = field.NewString(tableName, "topic_en")
	_thoTopic.TopicSub = field.NewString(tableName, "topic_sub")
	_thoTopic.TopicStatus = field.NewInt8(tableName, "topic_status")
	_thoTopic.TopicSort = field.NewInt32(tableName, "topic_sort")
	_thoTopic.TopicLetter = field.NewString(tableName, "topic_letter")
	_thoTopic.TopicColor = field.NewString(tableName, "topic_color")
	_thoTopic.TopicTpl = field.NewString(tableName, "topic_tpl")
	_thoTopic.TopicType = field.NewString(tableName, "topic_type")
	_thoTopic.TopicPic = field.NewString(tableName, "topic_pic")
	_thoTopic.TopicPicThumb = field.NewString(tableName, "topic_pic_thumb")
	_thoTopic.TopicPicSlide = field.NewString(tableName, "topic_pic_slide")
	_thoTopic.TopicKey = field.NewString(tableName, "topic_key")
	_thoTopic.TopicDes = field.NewString(tableName, "topic_des")
	_thoTopic.TopicTitle = field.NewString(tableName, "topic_title")
	_thoTopic.TopicBlurb = field.NewString(tableName, "topic_blurb")
	_thoTopic.TopicRemarks = field.NewString(tableName, "topic_remarks")
	_thoTopic.TopicLevel = field.NewInt8(tableName, "topic_level")
	_thoTopic.TopicUp = field.NewInt32(tableName, "topic_up")
	_thoTopic.TopicDown = field.NewInt32(tableName, "topic_down")
	_thoTopic.TopicScore = field.NewFloat64(tableName, "topic_score")
	_thoTopic.TopicScoreAll = field.NewInt32(tableName, "topic_score_all")
	_thoTopic.TopicScoreNum = field.NewInt32(tableName, "topic_score_num")
	_thoTopic.TopicHits = field.NewInt32(tableName, "topic_hits")
	_thoTopic.TopicHitsDay = field.NewInt32(tableName, "topic_hits_day")
	_thoTopic.TopicHitsWeek = field.NewInt32(tableName, "topic_hits_week")
	_thoTopic.TopicHitsMonth = field.NewInt32(tableName, "topic_hits_month")
	_thoTopic.TopicTime = field.NewInt32(tableName, "topic_time")
	_thoTopic.TopicTimeAdd = field.NewInt32(tableName, "topic_time_add")
	_thoTopic.TopicTimeHits = field.NewInt32(tableName, "topic_time_hits")
	_thoTopic.TopicTimeMake = field.NewInt32(tableName, "topic_time_make")
	_thoTopic.TopicTag = field.NewString(tableName, "topic_tag")
	_thoTopic.TopicRelVod = field.NewString(tableName, "topic_rel_vod")
	_thoTopic.TopicRelArt = field.NewString(tableName, "topic_rel_art")
	_thoTopic.TopicContent = field.NewString(tableName, "topic_content")
	_thoTopic.TopicExtend = field.NewString(tableName, "topic_extend")

	_thoTopic.fillFieldMap()

	return _thoTopic
}

type thoTopic struct {
	thoTopicDo

	ALL            field.Asterisk
	TopicID        field.Int32
	TopicName      field.String
	TopicEn        field.String
	TopicSub       field.String
	TopicStatus    field.Int8
	TopicSort      field.Int32
	TopicLetter    field.String
	TopicColor     field.String
	TopicTpl       field.String
	TopicType      field.String
	TopicPic       field.String
	TopicPicThumb  field.String
	TopicPicSlide  field.String
	TopicKey       field.String
	TopicDes       field.String
	TopicTitle     field.String
	TopicBlurb     field.String
	TopicRemarks   field.String
	TopicLevel     field.Int8
	TopicUp        field.Int32
	TopicDown      field.Int32
	TopicScore     field.Float64
	TopicScoreAll  field.Int32
	TopicScoreNum  field.Int32
	TopicHits      field.Int32
	TopicHitsDay   field.Int32
	TopicHitsWeek  field.Int32
	TopicHitsMonth field.Int32
	TopicTime      field.Int32
	TopicTimeAdd   field.Int32
	TopicTimeHits  field.Int32
	TopicTimeMake  field.Int32
	TopicTag       field.String
	TopicRelVod    field.String
	TopicRelArt    field.String
	TopicContent   field.String
	TopicExtend    field.String

	fieldMap map[string]field.Expr
}

func (t thoTopic) Table(newTableName string) *thoTopic {
	t.thoTopicDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thoTopic) As(alias string) *thoTopic {
	t.thoTopicDo.DO = *(t.thoTopicDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thoTopic) updateTableName(table string) *thoTopic {
	t.ALL = field.NewAsterisk(table)
	t.TopicID = field.NewInt32(table, "topic_id")
	t.TopicName = field.NewString(table, "topic_name")
	t.TopicEn = field.NewString(table, "topic_en")
	t.TopicSub = field.NewString(table, "topic_sub")
	t.TopicStatus = field.NewInt8(table, "topic_status")
	t.TopicSort = field.NewInt32(table, "topic_sort")
	t.TopicLetter = field.NewString(table, "topic_letter")
	t.TopicColor = field.NewString(table, "topic_color")
	t.TopicTpl = field.NewString(table, "topic_tpl")
	t.TopicType = field.NewString(table, "topic_type")
	t.TopicPic = field.NewString(table, "topic_pic")
	t.TopicPicThumb = field.NewString(table, "topic_pic_thumb")
	t.TopicPicSlide = field.NewString(table, "topic_pic_slide")
	t.TopicKey = field.NewString(table, "topic_key")
	t.TopicDes = field.NewString(table, "topic_des")
	t.TopicTitle = field.NewString(table, "topic_title")
	t.TopicBlurb = field.NewString(table, "topic_blurb")
	t.TopicRemarks = field.NewString(table, "topic_remarks")
	t.TopicLevel = field.NewInt8(table, "topic_level")
	t.TopicUp = field.NewInt32(table, "topic_up")
	t.TopicDown = field.NewInt32(table, "topic_down")
	t.TopicScore = field.NewFloat64(table, "topic_score")
	t.TopicScoreAll = field.NewInt32(table, "topic_score_all")
	t.TopicScoreNum = field.NewInt32(table, "topic_score_num")
	t.TopicHits = field.NewInt32(table, "topic_hits")
	t.TopicHitsDay = field.NewInt32(table, "topic_hits_day")
	t.TopicHitsWeek = field.NewInt32(table, "topic_hits_week")
	t.TopicHitsMonth = field.NewInt32(table, "topic_hits_month")
	t.TopicTime = field.NewInt32(table, "topic_time")
	t.TopicTimeAdd = field.NewInt32(table, "topic_time_add")
	t.TopicTimeHits = field.NewInt32(table, "topic_time_hits")
	t.TopicTimeMake = field.NewInt32(table, "topic_time_make")
	t.TopicTag = field.NewString(table, "topic_tag")
	t.TopicRelVod = field.NewString(table, "topic_rel_vod")
	t.TopicRelArt = field.NewString(table, "topic_rel_art")
	t.TopicContent = field.NewString(table, "topic_content")
	t.TopicExtend = field.NewString(table, "topic_extend")

	t.fillFieldMap()

	return t
}

func (t *thoTopic) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thoTopic) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 37)
	t.fieldMap["topic_id"] = t.TopicID
	t.fieldMap["topic_name"] = t.TopicName
	t.fieldMap["topic_en"] = t.TopicEn
	t.fieldMap["topic_sub"] = t.TopicSub
	t.fieldMap["topic_status"] = t.TopicStatus
	t.fieldMap["topic_sort"] = t.TopicSort
	t.fieldMap["topic_letter"] = t.TopicLetter
	t.fieldMap["topic_color"] = t.TopicColor
	t.fieldMap["topic_tpl"] = t.TopicTpl
	t.fieldMap["topic_type"] = t.TopicType
	t.fieldMap["topic_pic"] = t.TopicPic
	t.fieldMap["topic_pic_thumb"] = t.TopicPicThumb
	t.fieldMap["topic_pic_slide"] = t.TopicPicSlide
	t.fieldMap["topic_key"] = t.TopicKey
	t.fieldMap["topic_des"] = t.TopicDes
	t.fieldMap["topic_title"] = t.TopicTitle
	t.fieldMap["topic_blurb"] = t.TopicBlurb
	t.fieldMap["topic_remarks"] = t.TopicRemarks
	t.fieldMap["topic_level"] = t.TopicLevel
	t.fieldMap["topic_up"] = t.TopicUp
	t.fieldMap["topic_down"] = t.TopicDown
	t.fieldMap["topic_score"] = t.TopicScore
	t.fieldMap["topic_score_all"] = t.TopicScoreAll
	t.fieldMap["topic_score_num"] = t.TopicScoreNum
	t.fieldMap["topic_hits"] = t.TopicHits
	t.fieldMap["topic_hits_day"] = t.TopicHitsDay
	t.fieldMap["topic_hits_week"] = t.TopicHitsWeek
	t.fieldMap["topic_hits_month"] = t.TopicHitsMonth
	t.fieldMap["topic_time"] = t.TopicTime
	t.fieldMap["topic_time_add"] = t.TopicTimeAdd
	t.fieldMap["topic_time_hits"] = t.TopicTimeHits
	t.fieldMap["topic_time_make"] = t.TopicTimeMake
	t.fieldMap["topic_tag"] = t.TopicTag
	t.fieldMap["topic_rel_vod"] = t.TopicRelVod
	t.fieldMap["topic_rel_art"] = t.TopicRelArt
	t.fieldMap["topic_content"] = t.TopicContent
	t.fieldMap["topic_extend"] = t.TopicExtend
}

func (t thoTopic) clone(db *gorm.DB) thoTopic {
	t.thoTopicDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thoTopic) replaceDB(db *gorm.DB) thoTopic {
	t.thoTopicDo.ReplaceDB(db)
	return t
}

type thoTopicDo struct{ gen.DO }

type IThoTopicDo interface {
	gen.SubQuery
	Debug() IThoTopicDo
	WithContext(ctx context.Context) IThoTopicDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThoTopicDo
	WriteDB() IThoTopicDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThoTopicDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThoTopicDo
	Not(conds ...gen.Condition) IThoTopicDo
	Or(conds ...gen.Condition) IThoTopicDo
	Select(conds ...field.Expr) IThoTopicDo
	Where(conds ...gen.Condition) IThoTopicDo
	Order(conds ...field.Expr) IThoTopicDo
	Distinct(cols ...field.Expr) IThoTopicDo
	Omit(cols ...field.Expr) IThoTopicDo
	Join(table schema.Tabler, on ...field.Expr) IThoTopicDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThoTopicDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThoTopicDo
	Group(cols ...field.Expr) IThoTopicDo
	Having(conds ...gen.Condition) IThoTopicDo
	Limit(limit int) IThoTopicDo
	Offset(offset int) IThoTopicDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThoTopicDo
	Unscoped() IThoTopicDo
	Create(values ...*model.ThoTopic) error
	CreateInBatches(values []*model.ThoTopic, batchSize int) error
	Save(values ...*model.ThoTopic) error
	First() (*model.ThoTopic, error)
	Take() (*model.ThoTopic, error)
	Last() (*model.ThoTopic, error)
	Find() ([]*model.ThoTopic, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoTopic, err error)
	FindInBatches(result *[]*model.ThoTopic, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThoTopic) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThoTopicDo
	Assign(attrs ...field.AssignExpr) IThoTopicDo
	Joins(fields ...field.RelationField) IThoTopicDo
	Preload(fields ...field.RelationField) IThoTopicDo
	FirstOrInit() (*model.ThoTopic, error)
	FirstOrCreate() (*model.ThoTopic, error)
	FindByPage(offset int, limit int) (result []*model.ThoTopic, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThoTopicDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thoTopicDo) Debug() IThoTopicDo {
	return t.withDO(t.DO.Debug())
}

func (t thoTopicDo) WithContext(ctx context.Context) IThoTopicDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thoTopicDo) ReadDB() IThoTopicDo {
	return t.Clauses(dbresolver.Read)
}

func (t thoTopicDo) WriteDB() IThoTopicDo {
	return t.Clauses(dbresolver.Write)
}

func (t thoTopicDo) Session(config *gorm.Session) IThoTopicDo {
	return t.withDO(t.DO.Session(config))
}

func (t thoTopicDo) Clauses(conds ...clause.Expression) IThoTopicDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thoTopicDo) Returning(value interface{}, columns ...string) IThoTopicDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thoTopicDo) Not(conds ...gen.Condition) IThoTopicDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thoTopicDo) Or(conds ...gen.Condition) IThoTopicDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thoTopicDo) Select(conds ...field.Expr) IThoTopicDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thoTopicDo) Where(conds ...gen.Condition) IThoTopicDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thoTopicDo) Order(conds ...field.Expr) IThoTopicDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thoTopicDo) Distinct(cols ...field.Expr) IThoTopicDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thoTopicDo) Omit(cols ...field.Expr) IThoTopicDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thoTopicDo) Join(table schema.Tabler, on ...field.Expr) IThoTopicDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thoTopicDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThoTopicDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thoTopicDo) RightJoin(table schema.Tabler, on ...field.Expr) IThoTopicDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thoTopicDo) Group(cols ...field.Expr) IThoTopicDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thoTopicDo) Having(conds ...gen.Condition) IThoTopicDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thoTopicDo) Limit(limit int) IThoTopicDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thoTopicDo) Offset(offset int) IThoTopicDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thoTopicDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThoTopicDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thoTopicDo) Unscoped() IThoTopicDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thoTopicDo) Create(values ...*model.ThoTopic) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thoTopicDo) CreateInBatches(values []*model.ThoTopic, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thoTopicDo) Save(values ...*model.ThoTopic) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thoTopicDo) First() (*model.ThoTopic, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoTopic), nil
	}
}

func (t thoTopicDo) Take() (*model.ThoTopic, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoTopic), nil
	}
}

func (t thoTopicDo) Last() (*model.ThoTopic, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoTopic), nil
	}
}

func (t thoTopicDo) Find() ([]*model.ThoTopic, error) {
	result, err := t.DO.Find()
	return result.([]*model.ThoTopic), err
}

func (t thoTopicDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoTopic, err error) {
	buf := make([]*model.ThoTopic, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thoTopicDo) FindInBatches(result *[]*model.ThoTopic, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thoTopicDo) Attrs(attrs ...field.AssignExpr) IThoTopicDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thoTopicDo) Assign(attrs ...field.AssignExpr) IThoTopicDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thoTopicDo) Joins(fields ...field.RelationField) IThoTopicDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thoTopicDo) Preload(fields ...field.RelationField) IThoTopicDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thoTopicDo) FirstOrInit() (*model.ThoTopic, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoTopic), nil
	}
}

func (t thoTopicDo) FirstOrCreate() (*model.ThoTopic, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoTopic), nil
	}
}

func (t thoTopicDo) FindByPage(offset int, limit int) (result []*model.ThoTopic, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thoTopicDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thoTopicDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thoTopicDo) Delete(models ...*model.ThoTopic) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thoTopicDo) withDO(do gen.Dao) *thoTopicDo {
	t.DO = *do.(*gen.DO)
	return t
}
