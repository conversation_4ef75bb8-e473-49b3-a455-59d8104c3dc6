// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/wilsonce/rabbitmv_bot/dao/model"
)

func newThoUser(db *gorm.DB, opts ...gen.DOOption) thoUser {
	_thoUser := thoUser{}

	_thoUser.thoUserDo.UseDB(db, opts...)
	_thoUser.thoUserDo.UseModel(&model.ThoUser{})

	tableName := _thoUser.thoUserDo.TableName()
	_thoUser.ALL = field.NewAsterisk(tableName)
	_thoUser.UserID = field.NewInt32(tableName, "user_id")
	_thoUser.GroupID = field.NewInt32(tableName, "group_id")
	_thoUser.UserName = field.NewString(tableName, "user_name")
	_thoUser.UserPwd = field.NewString(tableName, "user_pwd")
	_thoUser.UserNickName = field.NewString(tableName, "user_nick_name")
	_thoUser.UserQq = field.NewString(tableName, "user_qq")
	_thoUser.UserEmail = field.NewString(tableName, "user_email")
	_thoUser.UserPhone = field.NewString(tableName, "user_phone")
	_thoUser.UserStatus = field.NewInt8(tableName, "user_status")
	_thoUser.UserPortrait = field.NewString(tableName, "user_portrait")
	_thoUser.UserPortraitThumb = field.NewString(tableName, "user_portrait_thumb")
	_thoUser.UserOpenidQq = field.NewString(tableName, "user_openid_qq")
	_thoUser.UserOpenidWeixin = field.NewString(tableName, "user_openid_weixin")
	_thoUser.UserQuestion = field.NewString(tableName, "user_question")
	_thoUser.UserAnswer = field.NewString(tableName, "user_answer")
	_thoUser.UserPoints = field.NewInt32(tableName, "user_points")
	_thoUser.UserPointsFroze = field.NewInt32(tableName, "user_points_froze")
	_thoUser.UserRegTime = field.NewInt32(tableName, "user_reg_time")
	_thoUser.UserRegIP = field.NewInt32(tableName, "user_reg_ip")
	_thoUser.UserLoginTime = field.NewInt32(tableName, "user_login_time")
	_thoUser.UserLoginIP = field.NewInt32(tableName, "user_login_ip")
	_thoUser.UserLastLoginTime = field.NewInt32(tableName, "user_last_login_time")
	_thoUser.UserLastLoginIP = field.NewInt32(tableName, "user_last_login_ip")
	_thoUser.UserLoginNum = field.NewInt32(tableName, "user_login_num")
	_thoUser.UserExtend = field.NewInt32(tableName, "user_extend")
	_thoUser.UserRandom = field.NewString(tableName, "user_random")
	_thoUser.UserEndTime = field.NewInt32(tableName, "user_end_time")
	_thoUser.UserPid = field.NewInt32(tableName, "user_pid")
	_thoUser.UserPid2 = field.NewInt32(tableName, "user_pid_2")
	_thoUser.UserPid3 = field.NewInt32(tableName, "user_pid_3")

	_thoUser.fillFieldMap()

	return _thoUser
}

type thoUser struct {
	thoUserDo

	ALL               field.Asterisk
	UserID            field.Int32
	GroupID           field.Int32
	UserName          field.String
	UserPwd           field.String
	UserNickName      field.String
	UserQq            field.String
	UserEmail         field.String
	UserPhone         field.String
	UserStatus        field.Int8
	UserPortrait      field.String
	UserPortraitThumb field.String
	UserOpenidQq      field.String
	UserOpenidWeixin  field.String
	UserQuestion      field.String
	UserAnswer        field.String
	UserPoints        field.Int32
	UserPointsFroze   field.Int32
	UserRegTime       field.Int32
	UserRegIP         field.Int32
	UserLoginTime     field.Int32
	UserLoginIP       field.Int32
	UserLastLoginTime field.Int32
	UserLastLoginIP   field.Int32
	UserLoginNum      field.Int32
	UserExtend        field.Int32
	UserRandom        field.String
	UserEndTime       field.Int32
	UserPid           field.Int32
	UserPid2          field.Int32
	UserPid3          field.Int32

	fieldMap map[string]field.Expr
}

func (t thoUser) Table(newTableName string) *thoUser {
	t.thoUserDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thoUser) As(alias string) *thoUser {
	t.thoUserDo.DO = *(t.thoUserDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thoUser) updateTableName(table string) *thoUser {
	t.ALL = field.NewAsterisk(table)
	t.UserID = field.NewInt32(table, "user_id")
	t.GroupID = field.NewInt32(table, "group_id")
	t.UserName = field.NewString(table, "user_name")
	t.UserPwd = field.NewString(table, "user_pwd")
	t.UserNickName = field.NewString(table, "user_nick_name")
	t.UserQq = field.NewString(table, "user_qq")
	t.UserEmail = field.NewString(table, "user_email")
	t.UserPhone = field.NewString(table, "user_phone")
	t.UserStatus = field.NewInt8(table, "user_status")
	t.UserPortrait = field.NewString(table, "user_portrait")
	t.UserPortraitThumb = field.NewString(table, "user_portrait_thumb")
	t.UserOpenidQq = field.NewString(table, "user_openid_qq")
	t.UserOpenidWeixin = field.NewString(table, "user_openid_weixin")
	t.UserQuestion = field.NewString(table, "user_question")
	t.UserAnswer = field.NewString(table, "user_answer")
	t.UserPoints = field.NewInt32(table, "user_points")
	t.UserPointsFroze = field.NewInt32(table, "user_points_froze")
	t.UserRegTime = field.NewInt32(table, "user_reg_time")
	t.UserRegIP = field.NewInt32(table, "user_reg_ip")
	t.UserLoginTime = field.NewInt32(table, "user_login_time")
	t.UserLoginIP = field.NewInt32(table, "user_login_ip")
	t.UserLastLoginTime = field.NewInt32(table, "user_last_login_time")
	t.UserLastLoginIP = field.NewInt32(table, "user_last_login_ip")
	t.UserLoginNum = field.NewInt32(table, "user_login_num")
	t.UserExtend = field.NewInt32(table, "user_extend")
	t.UserRandom = field.NewString(table, "user_random")
	t.UserEndTime = field.NewInt32(table, "user_end_time")
	t.UserPid = field.NewInt32(table, "user_pid")
	t.UserPid2 = field.NewInt32(table, "user_pid_2")
	t.UserPid3 = field.NewInt32(table, "user_pid_3")

	t.fillFieldMap()

	return t
}

func (t *thoUser) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thoUser) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 30)
	t.fieldMap["user_id"] = t.UserID
	t.fieldMap["group_id"] = t.GroupID
	t.fieldMap["user_name"] = t.UserName
	t.fieldMap["user_pwd"] = t.UserPwd
	t.fieldMap["user_nick_name"] = t.UserNickName
	t.fieldMap["user_qq"] = t.UserQq
	t.fieldMap["user_email"] = t.UserEmail
	t.fieldMap["user_phone"] = t.UserPhone
	t.fieldMap["user_status"] = t.UserStatus
	t.fieldMap["user_portrait"] = t.UserPortrait
	t.fieldMap["user_portrait_thumb"] = t.UserPortraitThumb
	t.fieldMap["user_openid_qq"] = t.UserOpenidQq
	t.fieldMap["user_openid_weixin"] = t.UserOpenidWeixin
	t.fieldMap["user_question"] = t.UserQuestion
	t.fieldMap["user_answer"] = t.UserAnswer
	t.fieldMap["user_points"] = t.UserPoints
	t.fieldMap["user_points_froze"] = t.UserPointsFroze
	t.fieldMap["user_reg_time"] = t.UserRegTime
	t.fieldMap["user_reg_ip"] = t.UserRegIP
	t.fieldMap["user_login_time"] = t.UserLoginTime
	t.fieldMap["user_login_ip"] = t.UserLoginIP
	t.fieldMap["user_last_login_time"] = t.UserLastLoginTime
	t.fieldMap["user_last_login_ip"] = t.UserLastLoginIP
	t.fieldMap["user_login_num"] = t.UserLoginNum
	t.fieldMap["user_extend"] = t.UserExtend
	t.fieldMap["user_random"] = t.UserRandom
	t.fieldMap["user_end_time"] = t.UserEndTime
	t.fieldMap["user_pid"] = t.UserPid
	t.fieldMap["user_pid_2"] = t.UserPid2
	t.fieldMap["user_pid_3"] = t.UserPid3
}

func (t thoUser) clone(db *gorm.DB) thoUser {
	t.thoUserDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thoUser) replaceDB(db *gorm.DB) thoUser {
	t.thoUserDo.ReplaceDB(db)
	return t
}

type thoUserDo struct{ gen.DO }

type IThoUserDo interface {
	gen.SubQuery
	Debug() IThoUserDo
	WithContext(ctx context.Context) IThoUserDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThoUserDo
	WriteDB() IThoUserDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThoUserDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThoUserDo
	Not(conds ...gen.Condition) IThoUserDo
	Or(conds ...gen.Condition) IThoUserDo
	Select(conds ...field.Expr) IThoUserDo
	Where(conds ...gen.Condition) IThoUserDo
	Order(conds ...field.Expr) IThoUserDo
	Distinct(cols ...field.Expr) IThoUserDo
	Omit(cols ...field.Expr) IThoUserDo
	Join(table schema.Tabler, on ...field.Expr) IThoUserDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThoUserDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThoUserDo
	Group(cols ...field.Expr) IThoUserDo
	Having(conds ...gen.Condition) IThoUserDo
	Limit(limit int) IThoUserDo
	Offset(offset int) IThoUserDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThoUserDo
	Unscoped() IThoUserDo
	Create(values ...*model.ThoUser) error
	CreateInBatches(values []*model.ThoUser, batchSize int) error
	Save(values ...*model.ThoUser) error
	First() (*model.ThoUser, error)
	Take() (*model.ThoUser, error)
	Last() (*model.ThoUser, error)
	Find() ([]*model.ThoUser, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoUser, err error)
	FindInBatches(result *[]*model.ThoUser, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThoUser) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThoUserDo
	Assign(attrs ...field.AssignExpr) IThoUserDo
	Joins(fields ...field.RelationField) IThoUserDo
	Preload(fields ...field.RelationField) IThoUserDo
	FirstOrInit() (*model.ThoUser, error)
	FirstOrCreate() (*model.ThoUser, error)
	FindByPage(offset int, limit int) (result []*model.ThoUser, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThoUserDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thoUserDo) Debug() IThoUserDo {
	return t.withDO(t.DO.Debug())
}

func (t thoUserDo) WithContext(ctx context.Context) IThoUserDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thoUserDo) ReadDB() IThoUserDo {
	return t.Clauses(dbresolver.Read)
}

func (t thoUserDo) WriteDB() IThoUserDo {
	return t.Clauses(dbresolver.Write)
}

func (t thoUserDo) Session(config *gorm.Session) IThoUserDo {
	return t.withDO(t.DO.Session(config))
}

func (t thoUserDo) Clauses(conds ...clause.Expression) IThoUserDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thoUserDo) Returning(value interface{}, columns ...string) IThoUserDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thoUserDo) Not(conds ...gen.Condition) IThoUserDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thoUserDo) Or(conds ...gen.Condition) IThoUserDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thoUserDo) Select(conds ...field.Expr) IThoUserDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thoUserDo) Where(conds ...gen.Condition) IThoUserDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thoUserDo) Order(conds ...field.Expr) IThoUserDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thoUserDo) Distinct(cols ...field.Expr) IThoUserDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thoUserDo) Omit(cols ...field.Expr) IThoUserDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thoUserDo) Join(table schema.Tabler, on ...field.Expr) IThoUserDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thoUserDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThoUserDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thoUserDo) RightJoin(table schema.Tabler, on ...field.Expr) IThoUserDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thoUserDo) Group(cols ...field.Expr) IThoUserDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thoUserDo) Having(conds ...gen.Condition) IThoUserDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thoUserDo) Limit(limit int) IThoUserDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thoUserDo) Offset(offset int) IThoUserDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thoUserDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThoUserDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thoUserDo) Unscoped() IThoUserDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thoUserDo) Create(values ...*model.ThoUser) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thoUserDo) CreateInBatches(values []*model.ThoUser, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thoUserDo) Save(values ...*model.ThoUser) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thoUserDo) First() (*model.ThoUser, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoUser), nil
	}
}

func (t thoUserDo) Take() (*model.ThoUser, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoUser), nil
	}
}

func (t thoUserDo) Last() (*model.ThoUser, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoUser), nil
	}
}

func (t thoUserDo) Find() ([]*model.ThoUser, error) {
	result, err := t.DO.Find()
	return result.([]*model.ThoUser), err
}

func (t thoUserDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoUser, err error) {
	buf := make([]*model.ThoUser, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thoUserDo) FindInBatches(result *[]*model.ThoUser, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thoUserDo) Attrs(attrs ...field.AssignExpr) IThoUserDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thoUserDo) Assign(attrs ...field.AssignExpr) IThoUserDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thoUserDo) Joins(fields ...field.RelationField) IThoUserDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thoUserDo) Preload(fields ...field.RelationField) IThoUserDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thoUserDo) FirstOrInit() (*model.ThoUser, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoUser), nil
	}
}

func (t thoUserDo) FirstOrCreate() (*model.ThoUser, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoUser), nil
	}
}

func (t thoUserDo) FindByPage(offset int, limit int) (result []*model.ThoUser, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thoUserDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thoUserDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thoUserDo) Delete(models ...*model.ThoUser) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thoUserDo) withDO(do gen.Dao) *thoUserDo {
	t.DO = *do.(*gen.DO)
	return t
}
