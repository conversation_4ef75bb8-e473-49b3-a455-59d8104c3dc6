// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"
	"database/sql"

	"gorm.io/gorm"

	"gorm.io/gen"

	"gorm.io/plugin/dbresolver"
)

var (
	Q            = new(Query)
	ThoActor     *thoActor
	ThoAdmin     *thoAdmin
	ThoAnnex     *thoAnnex
	ThoArt       *thoArt
	ThoCard      *thoCard
	ThoCash      *thoCash
	ThoCjContent *thoCjContent
	ThoCjHistory *thoCjHistory
	ThoCjNode    *thoCjNode
	ThoCollect   *thoCollect
	ThoComment   *thoComment
	ThoGbook     *thoGbook
	ThoGroup     *thoGroup
	ThoLink      *thoLink
	ThoMsg       *thoMsg
	ThoOrder     *thoOrder
	ThoPlog      *thoPlog
	ThoRole      *thoRole
	ThoTopic     *thoTopic
	ThoType      *thoType
	ThoUlog      *thoUlog
	ThoUser      *thoUser
	ThoVisit     *thoVisit
	ThoVod       *thoVod
	ThoVodSearch *thoVodSearch
	ThoWebsite   *thoWebsite
)

func SetDefault(db *gorm.DB, opts ...gen.DOOption) {
	*Q = *Use(db, opts...)
	ThoActor = &Q.ThoActor
	ThoAdmin = &Q.ThoAdmin
	ThoAnnex = &Q.ThoAnnex
	ThoArt = &Q.ThoArt
	ThoCard = &Q.ThoCard
	ThoCash = &Q.ThoCash
	ThoCjContent = &Q.ThoCjContent
	ThoCjHistory = &Q.ThoCjHistory
	ThoCjNode = &Q.ThoCjNode
	ThoCollect = &Q.ThoCollect
	ThoComment = &Q.ThoComment
	ThoGbook = &Q.ThoGbook
	ThoGroup = &Q.ThoGroup
	ThoLink = &Q.ThoLink
	ThoMsg = &Q.ThoMsg
	ThoOrder = &Q.ThoOrder
	ThoPlog = &Q.ThoPlog
	ThoRole = &Q.ThoRole
	ThoTopic = &Q.ThoTopic
	ThoType = &Q.ThoType
	ThoUlog = &Q.ThoUlog
	ThoUser = &Q.ThoUser
	ThoVisit = &Q.ThoVisit
	ThoVod = &Q.ThoVod
	ThoVodSearch = &Q.ThoVodSearch
	ThoWebsite = &Q.ThoWebsite
}

func Use(db *gorm.DB, opts ...gen.DOOption) *Query {
	return &Query{
		db:           db,
		ThoActor:     newThoActor(db, opts...),
		ThoAdmin:     newThoAdmin(db, opts...),
		ThoAnnex:     newThoAnnex(db, opts...),
		ThoArt:       newThoArt(db, opts...),
		ThoCard:      newThoCard(db, opts...),
		ThoCash:      newThoCash(db, opts...),
		ThoCjContent: newThoCjContent(db, opts...),
		ThoCjHistory: newThoCjHistory(db, opts...),
		ThoCjNode:    newThoCjNode(db, opts...),
		ThoCollect:   newThoCollect(db, opts...),
		ThoComment:   newThoComment(db, opts...),
		ThoGbook:     newThoGbook(db, opts...),
		ThoGroup:     newThoGroup(db, opts...),
		ThoLink:      newThoLink(db, opts...),
		ThoMsg:       newThoMsg(db, opts...),
		ThoOrder:     newThoOrder(db, opts...),
		ThoPlog:      newThoPlog(db, opts...),
		ThoRole:      newThoRole(db, opts...),
		ThoTopic:     newThoTopic(db, opts...),
		ThoType:      newThoType(db, opts...),
		ThoUlog:      newThoUlog(db, opts...),
		ThoUser:      newThoUser(db, opts...),
		ThoVisit:     newThoVisit(db, opts...),
		ThoVod:       newThoVod(db, opts...),
		ThoVodSearch: newThoVodSearch(db, opts...),
		ThoWebsite:   newThoWebsite(db, opts...),
	}
}

type Query struct {
	db *gorm.DB

	ThoActor     thoActor
	ThoAdmin     thoAdmin
	ThoAnnex     thoAnnex
	ThoArt       thoArt
	ThoCard      thoCard
	ThoCash      thoCash
	ThoCjContent thoCjContent
	ThoCjHistory thoCjHistory
	ThoCjNode    thoCjNode
	ThoCollect   thoCollect
	ThoComment   thoComment
	ThoGbook     thoGbook
	ThoGroup     thoGroup
	ThoLink      thoLink
	ThoMsg       thoMsg
	ThoOrder     thoOrder
	ThoPlog      thoPlog
	ThoRole      thoRole
	ThoTopic     thoTopic
	ThoType      thoType
	ThoUlog      thoUlog
	ThoUser      thoUser
	ThoVisit     thoVisit
	ThoVod       thoVod
	ThoVodSearch thoVodSearch
	ThoWebsite   thoWebsite
}

func (q *Query) Available() bool { return q.db != nil }

func (q *Query) clone(db *gorm.DB) *Query {
	return &Query{
		db:           db,
		ThoActor:     q.ThoActor.clone(db),
		ThoAdmin:     q.ThoAdmin.clone(db),
		ThoAnnex:     q.ThoAnnex.clone(db),
		ThoArt:       q.ThoArt.clone(db),
		ThoCard:      q.ThoCard.clone(db),
		ThoCash:      q.ThoCash.clone(db),
		ThoCjContent: q.ThoCjContent.clone(db),
		ThoCjHistory: q.ThoCjHistory.clone(db),
		ThoCjNode:    q.ThoCjNode.clone(db),
		ThoCollect:   q.ThoCollect.clone(db),
		ThoComment:   q.ThoComment.clone(db),
		ThoGbook:     q.ThoGbook.clone(db),
		ThoGroup:     q.ThoGroup.clone(db),
		ThoLink:      q.ThoLink.clone(db),
		ThoMsg:       q.ThoMsg.clone(db),
		ThoOrder:     q.ThoOrder.clone(db),
		ThoPlog:      q.ThoPlog.clone(db),
		ThoRole:      q.ThoRole.clone(db),
		ThoTopic:     q.ThoTopic.clone(db),
		ThoType:      q.ThoType.clone(db),
		ThoUlog:      q.ThoUlog.clone(db),
		ThoUser:      q.ThoUser.clone(db),
		ThoVisit:     q.ThoVisit.clone(db),
		ThoVod:       q.ThoVod.clone(db),
		ThoVodSearch: q.ThoVodSearch.clone(db),
		ThoWebsite:   q.ThoWebsite.clone(db),
	}
}

func (q *Query) ReadDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Read))
}

func (q *Query) WriteDB() *Query {
	return q.ReplaceDB(q.db.Clauses(dbresolver.Write))
}

func (q *Query) ReplaceDB(db *gorm.DB) *Query {
	return &Query{
		db:           db,
		ThoActor:     q.ThoActor.replaceDB(db),
		ThoAdmin:     q.ThoAdmin.replaceDB(db),
		ThoAnnex:     q.ThoAnnex.replaceDB(db),
		ThoArt:       q.ThoArt.replaceDB(db),
		ThoCard:      q.ThoCard.replaceDB(db),
		ThoCash:      q.ThoCash.replaceDB(db),
		ThoCjContent: q.ThoCjContent.replaceDB(db),
		ThoCjHistory: q.ThoCjHistory.replaceDB(db),
		ThoCjNode:    q.ThoCjNode.replaceDB(db),
		ThoCollect:   q.ThoCollect.replaceDB(db),
		ThoComment:   q.ThoComment.replaceDB(db),
		ThoGbook:     q.ThoGbook.replaceDB(db),
		ThoGroup:     q.ThoGroup.replaceDB(db),
		ThoLink:      q.ThoLink.replaceDB(db),
		ThoMsg:       q.ThoMsg.replaceDB(db),
		ThoOrder:     q.ThoOrder.replaceDB(db),
		ThoPlog:      q.ThoPlog.replaceDB(db),
		ThoRole:      q.ThoRole.replaceDB(db),
		ThoTopic:     q.ThoTopic.replaceDB(db),
		ThoType:      q.ThoType.replaceDB(db),
		ThoUlog:      q.ThoUlog.replaceDB(db),
		ThoUser:      q.ThoUser.replaceDB(db),
		ThoVisit:     q.ThoVisit.replaceDB(db),
		ThoVod:       q.ThoVod.replaceDB(db),
		ThoVodSearch: q.ThoVodSearch.replaceDB(db),
		ThoWebsite:   q.ThoWebsite.replaceDB(db),
	}
}

type queryCtx struct {
	ThoActor     IThoActorDo
	ThoAdmin     IThoAdminDo
	ThoAnnex     IThoAnnexDo
	ThoArt       IThoArtDo
	ThoCard      IThoCardDo
	ThoCash      IThoCashDo
	ThoCjContent IThoCjContentDo
	ThoCjHistory IThoCjHistoryDo
	ThoCjNode    IThoCjNodeDo
	ThoCollect   IThoCollectDo
	ThoComment   IThoCommentDo
	ThoGbook     IThoGbookDo
	ThoGroup     IThoGroupDo
	ThoLink      IThoLinkDo
	ThoMsg       IThoMsgDo
	ThoOrder     IThoOrderDo
	ThoPlog      IThoPlogDo
	ThoRole      IThoRoleDo
	ThoTopic     IThoTopicDo
	ThoType      IThoTypeDo
	ThoUlog      IThoUlogDo
	ThoUser      IThoUserDo
	ThoVisit     IThoVisitDo
	ThoVod       IThoVodDo
	ThoVodSearch IThoVodSearchDo
	ThoWebsite   IThoWebsiteDo
}

func (q *Query) WithContext(ctx context.Context) *queryCtx {
	return &queryCtx{
		ThoActor:     q.ThoActor.WithContext(ctx),
		ThoAdmin:     q.ThoAdmin.WithContext(ctx),
		ThoAnnex:     q.ThoAnnex.WithContext(ctx),
		ThoArt:       q.ThoArt.WithContext(ctx),
		ThoCard:      q.ThoCard.WithContext(ctx),
		ThoCash:      q.ThoCash.WithContext(ctx),
		ThoCjContent: q.ThoCjContent.WithContext(ctx),
		ThoCjHistory: q.ThoCjHistory.WithContext(ctx),
		ThoCjNode:    q.ThoCjNode.WithContext(ctx),
		ThoCollect:   q.ThoCollect.WithContext(ctx),
		ThoComment:   q.ThoComment.WithContext(ctx),
		ThoGbook:     q.ThoGbook.WithContext(ctx),
		ThoGroup:     q.ThoGroup.WithContext(ctx),
		ThoLink:      q.ThoLink.WithContext(ctx),
		ThoMsg:       q.ThoMsg.WithContext(ctx),
		ThoOrder:     q.ThoOrder.WithContext(ctx),
		ThoPlog:      q.ThoPlog.WithContext(ctx),
		ThoRole:      q.ThoRole.WithContext(ctx),
		ThoTopic:     q.ThoTopic.WithContext(ctx),
		ThoType:      q.ThoType.WithContext(ctx),
		ThoUlog:      q.ThoUlog.WithContext(ctx),
		ThoUser:      q.ThoUser.WithContext(ctx),
		ThoVisit:     q.ThoVisit.WithContext(ctx),
		ThoVod:       q.ThoVod.WithContext(ctx),
		ThoVodSearch: q.ThoVodSearch.WithContext(ctx),
		ThoWebsite:   q.ThoWebsite.WithContext(ctx),
	}
}

func (q *Query) Transaction(fc func(tx *Query) error, opts ...*sql.TxOptions) error {
	return q.db.Transaction(func(tx *gorm.DB) error { return fc(q.clone(tx)) }, opts...)
}

func (q *Query) Begin(opts ...*sql.TxOptions) *QueryTx {
	tx := q.db.Begin(opts...)
	return &QueryTx{Query: q.clone(tx), Error: tx.Error}
}

type QueryTx struct {
	*Query
	Error error
}

func (q *QueryTx) Commit() error {
	return q.db.Commit().Error
}

func (q *QueryTx) Rollback() error {
	return q.db.Rollback().Error
}

func (q *QueryTx) SavePoint(name string) error {
	return q.db.SavePoint(name).Error
}

func (q *QueryTx) RollbackTo(name string) error {
	return q.db.RollbackTo(name).Error
}
