// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/wilsonce/rabbitmv_bot/dao/model"
)

func newThoOrder(db *gorm.DB, opts ...gen.DOOption) thoOrder {
	_thoOrder := thoOrder{}

	_thoOrder.thoOrderDo.UseDB(db, opts...)
	_thoOrder.thoOrderDo.UseModel(&model.ThoOrder{})

	tableName := _thoOrder.thoOrderDo.TableName()
	_thoOrder.ALL = field.NewAsterisk(tableName)
	_thoOrder.OrderID = field.NewInt32(tableName, "order_id")
	_thoOrder.UserID = field.NewInt32(tableName, "user_id")
	_thoOrder.OrderStatus = field.NewInt8(tableName, "order_status")
	_thoOrder.OrderCode = field.NewString(tableName, "order_code")
	_thoOrder.OrderPrice = field.NewFloat64(tableName, "order_price")
	_thoOrder.OrderTime = field.NewInt32(tableName, "order_time")
	_thoOrder.OrderPoints = field.NewInt32(tableName, "order_points")
	_thoOrder.OrderPayType = field.NewString(tableName, "order_pay_type")
	_thoOrder.OrderPayTime = field.NewInt32(tableName, "order_pay_time")
	_thoOrder.OrderRemarks = field.NewString(tableName, "order_remarks")

	_thoOrder.fillFieldMap()

	return _thoOrder
}

type thoOrder struct {
	thoOrderDo

	ALL          field.Asterisk
	OrderID      field.Int32
	UserID       field.Int32
	OrderStatus  field.Int8
	OrderCode    field.String
	OrderPrice   field.Float64
	OrderTime    field.Int32
	OrderPoints  field.Int32
	OrderPayType field.String
	OrderPayTime field.Int32
	OrderRemarks field.String

	fieldMap map[string]field.Expr
}

func (t thoOrder) Table(newTableName string) *thoOrder {
	t.thoOrderDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thoOrder) As(alias string) *thoOrder {
	t.thoOrderDo.DO = *(t.thoOrderDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thoOrder) updateTableName(table string) *thoOrder {
	t.ALL = field.NewAsterisk(table)
	t.OrderID = field.NewInt32(table, "order_id")
	t.UserID = field.NewInt32(table, "user_id")
	t.OrderStatus = field.NewInt8(table, "order_status")
	t.OrderCode = field.NewString(table, "order_code")
	t.OrderPrice = field.NewFloat64(table, "order_price")
	t.OrderTime = field.NewInt32(table, "order_time")
	t.OrderPoints = field.NewInt32(table, "order_points")
	t.OrderPayType = field.NewString(table, "order_pay_type")
	t.OrderPayTime = field.NewInt32(table, "order_pay_time")
	t.OrderRemarks = field.NewString(table, "order_remarks")

	t.fillFieldMap()

	return t
}

func (t *thoOrder) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thoOrder) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 10)
	t.fieldMap["order_id"] = t.OrderID
	t.fieldMap["user_id"] = t.UserID
	t.fieldMap["order_status"] = t.OrderStatus
	t.fieldMap["order_code"] = t.OrderCode
	t.fieldMap["order_price"] = t.OrderPrice
	t.fieldMap["order_time"] = t.OrderTime
	t.fieldMap["order_points"] = t.OrderPoints
	t.fieldMap["order_pay_type"] = t.OrderPayType
	t.fieldMap["order_pay_time"] = t.OrderPayTime
	t.fieldMap["order_remarks"] = t.OrderRemarks
}

func (t thoOrder) clone(db *gorm.DB) thoOrder {
	t.thoOrderDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thoOrder) replaceDB(db *gorm.DB) thoOrder {
	t.thoOrderDo.ReplaceDB(db)
	return t
}

type thoOrderDo struct{ gen.DO }

type IThoOrderDo interface {
	gen.SubQuery
	Debug() IThoOrderDo
	WithContext(ctx context.Context) IThoOrderDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThoOrderDo
	WriteDB() IThoOrderDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThoOrderDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThoOrderDo
	Not(conds ...gen.Condition) IThoOrderDo
	Or(conds ...gen.Condition) IThoOrderDo
	Select(conds ...field.Expr) IThoOrderDo
	Where(conds ...gen.Condition) IThoOrderDo
	Order(conds ...field.Expr) IThoOrderDo
	Distinct(cols ...field.Expr) IThoOrderDo
	Omit(cols ...field.Expr) IThoOrderDo
	Join(table schema.Tabler, on ...field.Expr) IThoOrderDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThoOrderDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThoOrderDo
	Group(cols ...field.Expr) IThoOrderDo
	Having(conds ...gen.Condition) IThoOrderDo
	Limit(limit int) IThoOrderDo
	Offset(offset int) IThoOrderDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThoOrderDo
	Unscoped() IThoOrderDo
	Create(values ...*model.ThoOrder) error
	CreateInBatches(values []*model.ThoOrder, batchSize int) error
	Save(values ...*model.ThoOrder) error
	First() (*model.ThoOrder, error)
	Take() (*model.ThoOrder, error)
	Last() (*model.ThoOrder, error)
	Find() ([]*model.ThoOrder, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoOrder, err error)
	FindInBatches(result *[]*model.ThoOrder, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThoOrder) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThoOrderDo
	Assign(attrs ...field.AssignExpr) IThoOrderDo
	Joins(fields ...field.RelationField) IThoOrderDo
	Preload(fields ...field.RelationField) IThoOrderDo
	FirstOrInit() (*model.ThoOrder, error)
	FirstOrCreate() (*model.ThoOrder, error)
	FindByPage(offset int, limit int) (result []*model.ThoOrder, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThoOrderDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thoOrderDo) Debug() IThoOrderDo {
	return t.withDO(t.DO.Debug())
}

func (t thoOrderDo) WithContext(ctx context.Context) IThoOrderDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thoOrderDo) ReadDB() IThoOrderDo {
	return t.Clauses(dbresolver.Read)
}

func (t thoOrderDo) WriteDB() IThoOrderDo {
	return t.Clauses(dbresolver.Write)
}

func (t thoOrderDo) Session(config *gorm.Session) IThoOrderDo {
	return t.withDO(t.DO.Session(config))
}

func (t thoOrderDo) Clauses(conds ...clause.Expression) IThoOrderDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thoOrderDo) Returning(value interface{}, columns ...string) IThoOrderDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thoOrderDo) Not(conds ...gen.Condition) IThoOrderDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thoOrderDo) Or(conds ...gen.Condition) IThoOrderDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thoOrderDo) Select(conds ...field.Expr) IThoOrderDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thoOrderDo) Where(conds ...gen.Condition) IThoOrderDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thoOrderDo) Order(conds ...field.Expr) IThoOrderDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thoOrderDo) Distinct(cols ...field.Expr) IThoOrderDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thoOrderDo) Omit(cols ...field.Expr) IThoOrderDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thoOrderDo) Join(table schema.Tabler, on ...field.Expr) IThoOrderDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thoOrderDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThoOrderDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thoOrderDo) RightJoin(table schema.Tabler, on ...field.Expr) IThoOrderDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thoOrderDo) Group(cols ...field.Expr) IThoOrderDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thoOrderDo) Having(conds ...gen.Condition) IThoOrderDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thoOrderDo) Limit(limit int) IThoOrderDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thoOrderDo) Offset(offset int) IThoOrderDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thoOrderDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThoOrderDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thoOrderDo) Unscoped() IThoOrderDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thoOrderDo) Create(values ...*model.ThoOrder) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thoOrderDo) CreateInBatches(values []*model.ThoOrder, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thoOrderDo) Save(values ...*model.ThoOrder) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thoOrderDo) First() (*model.ThoOrder, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoOrder), nil
	}
}

func (t thoOrderDo) Take() (*model.ThoOrder, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoOrder), nil
	}
}

func (t thoOrderDo) Last() (*model.ThoOrder, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoOrder), nil
	}
}

func (t thoOrderDo) Find() ([]*model.ThoOrder, error) {
	result, err := t.DO.Find()
	return result.([]*model.ThoOrder), err
}

func (t thoOrderDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoOrder, err error) {
	buf := make([]*model.ThoOrder, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thoOrderDo) FindInBatches(result *[]*model.ThoOrder, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thoOrderDo) Attrs(attrs ...field.AssignExpr) IThoOrderDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thoOrderDo) Assign(attrs ...field.AssignExpr) IThoOrderDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thoOrderDo) Joins(fields ...field.RelationField) IThoOrderDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thoOrderDo) Preload(fields ...field.RelationField) IThoOrderDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thoOrderDo) FirstOrInit() (*model.ThoOrder, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoOrder), nil
	}
}

func (t thoOrderDo) FirstOrCreate() (*model.ThoOrder, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoOrder), nil
	}
}

func (t thoOrderDo) FindByPage(offset int, limit int) (result []*model.ThoOrder, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thoOrderDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thoOrderDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thoOrderDo) Delete(models ...*model.ThoOrder) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thoOrderDo) withDO(do gen.Dao) *thoOrderDo {
	t.DO = *do.(*gen.DO)
	return t
}
