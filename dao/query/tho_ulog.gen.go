// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/wilsonce/rabbitmv_bot/dao/model"
)

func newThoUlog(db *gorm.DB, opts ...gen.DOOption) thoUlog {
	_thoUlog := thoUlog{}

	_thoUlog.thoUlogDo.UseDB(db, opts...)
	_thoUlog.thoUlogDo.UseModel(&model.ThoUlog{})

	tableName := _thoUlog.thoUlogDo.TableName()
	_thoUlog.ALL = field.NewAsterisk(tableName)
	_thoUlog.UlogID = field.NewInt32(tableName, "ulog_id")
	_thoUlog.UserID = field.NewInt32(tableName, "user_id")
	_thoUlog.UlogMid = field.NewInt8(tableName, "ulog_mid")
	_thoUlog.UlogType = field.NewInt8(tableName, "ulog_type")
	_thoUlog.UlogRid = field.NewInt32(tableName, "ulog_rid")
	_thoUlog.UlogSid = field.NewInt8(tableName, "ulog_sid")
	_thoUlog.UlogNid = field.NewInt32(tableName, "ulog_nid")
	_thoUlog.UlogPoints = field.NewInt32(tableName, "ulog_points")
	_thoUlog.UlogTime = field.NewInt32(tableName, "ulog_time")

	_thoUlog.fillFieldMap()

	return _thoUlog
}

type thoUlog struct {
	thoUlogDo

	ALL        field.Asterisk
	UlogID     field.Int32
	UserID     field.Int32
	UlogMid    field.Int8
	UlogType   field.Int8
	UlogRid    field.Int32
	UlogSid    field.Int8
	UlogNid    field.Int32
	UlogPoints field.Int32
	UlogTime   field.Int32

	fieldMap map[string]field.Expr
}

func (t thoUlog) Table(newTableName string) *thoUlog {
	t.thoUlogDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thoUlog) As(alias string) *thoUlog {
	t.thoUlogDo.DO = *(t.thoUlogDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thoUlog) updateTableName(table string) *thoUlog {
	t.ALL = field.NewAsterisk(table)
	t.UlogID = field.NewInt32(table, "ulog_id")
	t.UserID = field.NewInt32(table, "user_id")
	t.UlogMid = field.NewInt8(table, "ulog_mid")
	t.UlogType = field.NewInt8(table, "ulog_type")
	t.UlogRid = field.NewInt32(table, "ulog_rid")
	t.UlogSid = field.NewInt8(table, "ulog_sid")
	t.UlogNid = field.NewInt32(table, "ulog_nid")
	t.UlogPoints = field.NewInt32(table, "ulog_points")
	t.UlogTime = field.NewInt32(table, "ulog_time")

	t.fillFieldMap()

	return t
}

func (t *thoUlog) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thoUlog) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 9)
	t.fieldMap["ulog_id"] = t.UlogID
	t.fieldMap["user_id"] = t.UserID
	t.fieldMap["ulog_mid"] = t.UlogMid
	t.fieldMap["ulog_type"] = t.UlogType
	t.fieldMap["ulog_rid"] = t.UlogRid
	t.fieldMap["ulog_sid"] = t.UlogSid
	t.fieldMap["ulog_nid"] = t.UlogNid
	t.fieldMap["ulog_points"] = t.UlogPoints
	t.fieldMap["ulog_time"] = t.UlogTime
}

func (t thoUlog) clone(db *gorm.DB) thoUlog {
	t.thoUlogDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thoUlog) replaceDB(db *gorm.DB) thoUlog {
	t.thoUlogDo.ReplaceDB(db)
	return t
}

type thoUlogDo struct{ gen.DO }

type IThoUlogDo interface {
	gen.SubQuery
	Debug() IThoUlogDo
	WithContext(ctx context.Context) IThoUlogDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThoUlogDo
	WriteDB() IThoUlogDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThoUlogDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThoUlogDo
	Not(conds ...gen.Condition) IThoUlogDo
	Or(conds ...gen.Condition) IThoUlogDo
	Select(conds ...field.Expr) IThoUlogDo
	Where(conds ...gen.Condition) IThoUlogDo
	Order(conds ...field.Expr) IThoUlogDo
	Distinct(cols ...field.Expr) IThoUlogDo
	Omit(cols ...field.Expr) IThoUlogDo
	Join(table schema.Tabler, on ...field.Expr) IThoUlogDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThoUlogDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThoUlogDo
	Group(cols ...field.Expr) IThoUlogDo
	Having(conds ...gen.Condition) IThoUlogDo
	Limit(limit int) IThoUlogDo
	Offset(offset int) IThoUlogDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThoUlogDo
	Unscoped() IThoUlogDo
	Create(values ...*model.ThoUlog) error
	CreateInBatches(values []*model.ThoUlog, batchSize int) error
	Save(values ...*model.ThoUlog) error
	First() (*model.ThoUlog, error)
	Take() (*model.ThoUlog, error)
	Last() (*model.ThoUlog, error)
	Find() ([]*model.ThoUlog, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoUlog, err error)
	FindInBatches(result *[]*model.ThoUlog, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThoUlog) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThoUlogDo
	Assign(attrs ...field.AssignExpr) IThoUlogDo
	Joins(fields ...field.RelationField) IThoUlogDo
	Preload(fields ...field.RelationField) IThoUlogDo
	FirstOrInit() (*model.ThoUlog, error)
	FirstOrCreate() (*model.ThoUlog, error)
	FindByPage(offset int, limit int) (result []*model.ThoUlog, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThoUlogDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thoUlogDo) Debug() IThoUlogDo {
	return t.withDO(t.DO.Debug())
}

func (t thoUlogDo) WithContext(ctx context.Context) IThoUlogDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thoUlogDo) ReadDB() IThoUlogDo {
	return t.Clauses(dbresolver.Read)
}

func (t thoUlogDo) WriteDB() IThoUlogDo {
	return t.Clauses(dbresolver.Write)
}

func (t thoUlogDo) Session(config *gorm.Session) IThoUlogDo {
	return t.withDO(t.DO.Session(config))
}

func (t thoUlogDo) Clauses(conds ...clause.Expression) IThoUlogDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thoUlogDo) Returning(value interface{}, columns ...string) IThoUlogDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thoUlogDo) Not(conds ...gen.Condition) IThoUlogDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thoUlogDo) Or(conds ...gen.Condition) IThoUlogDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thoUlogDo) Select(conds ...field.Expr) IThoUlogDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thoUlogDo) Where(conds ...gen.Condition) IThoUlogDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thoUlogDo) Order(conds ...field.Expr) IThoUlogDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thoUlogDo) Distinct(cols ...field.Expr) IThoUlogDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thoUlogDo) Omit(cols ...field.Expr) IThoUlogDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thoUlogDo) Join(table schema.Tabler, on ...field.Expr) IThoUlogDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thoUlogDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThoUlogDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thoUlogDo) RightJoin(table schema.Tabler, on ...field.Expr) IThoUlogDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thoUlogDo) Group(cols ...field.Expr) IThoUlogDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thoUlogDo) Having(conds ...gen.Condition) IThoUlogDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thoUlogDo) Limit(limit int) IThoUlogDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thoUlogDo) Offset(offset int) IThoUlogDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thoUlogDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThoUlogDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thoUlogDo) Unscoped() IThoUlogDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thoUlogDo) Create(values ...*model.ThoUlog) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thoUlogDo) CreateInBatches(values []*model.ThoUlog, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thoUlogDo) Save(values ...*model.ThoUlog) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thoUlogDo) First() (*model.ThoUlog, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoUlog), nil
	}
}

func (t thoUlogDo) Take() (*model.ThoUlog, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoUlog), nil
	}
}

func (t thoUlogDo) Last() (*model.ThoUlog, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoUlog), nil
	}
}

func (t thoUlogDo) Find() ([]*model.ThoUlog, error) {
	result, err := t.DO.Find()
	return result.([]*model.ThoUlog), err
}

func (t thoUlogDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoUlog, err error) {
	buf := make([]*model.ThoUlog, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thoUlogDo) FindInBatches(result *[]*model.ThoUlog, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thoUlogDo) Attrs(attrs ...field.AssignExpr) IThoUlogDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thoUlogDo) Assign(attrs ...field.AssignExpr) IThoUlogDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thoUlogDo) Joins(fields ...field.RelationField) IThoUlogDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thoUlogDo) Preload(fields ...field.RelationField) IThoUlogDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thoUlogDo) FirstOrInit() (*model.ThoUlog, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoUlog), nil
	}
}

func (t thoUlogDo) FirstOrCreate() (*model.ThoUlog, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoUlog), nil
	}
}

func (t thoUlogDo) FindByPage(offset int, limit int) (result []*model.ThoUlog, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thoUlogDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thoUlogDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thoUlogDo) Delete(models ...*model.ThoUlog) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thoUlogDo) withDO(do gen.Dao) *thoUlogDo {
	t.DO = *do.(*gen.DO)
	return t
}
