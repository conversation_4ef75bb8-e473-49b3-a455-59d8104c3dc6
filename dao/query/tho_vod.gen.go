// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/wilsonce/rabbitmv_bot/dao/model"
)

func newThoVod(db *gorm.DB, opts ...gen.DOOption) thoVod {
	_thoVod := thoVod{}

	_thoVod.thoVodDo.UseDB(db, opts...)
	_thoVod.thoVodDo.UseModel(&model.ThoVod{})

	tableName := _thoVod.thoVodDo.TableName()
	_thoVod.ALL = field.NewAsterisk(tableName)
	_thoVod.VodID = field.NewInt32(tableName, "vod_id")
	_thoVod.TypeID = field.NewInt32(tableName, "type_id")
	_thoVod.TypeId1 = field.NewInt32(tableName, "type_id_1")
	_thoVod.GroupID = field.NewInt32(tableName, "group_id")
	_thoVod.VodName = field.NewString(tableName, "vod_name")
	_thoVod.VodSub = field.NewString(tableName, "vod_sub")
	_thoVod.VodEn = field.NewString(tableName, "vod_en")
	_thoVod.VodStatus = field.NewInt8(tableName, "vod_status")
	_thoVod.VodLetter = field.NewString(tableName, "vod_letter")
	_thoVod.VodColor = field.NewString(tableName, "vod_color")
	_thoVod.VodTag = field.NewString(tableName, "vod_tag")
	_thoVod.VodClass = field.NewString(tableName, "vod_class")
	_thoVod.VodPic = field.NewString(tableName, "vod_pic")
	_thoVod.VodPicThumb = field.NewString(tableName, "vod_pic_thumb")
	_thoVod.VodPicSlide = field.NewString(tableName, "vod_pic_slide")
	_thoVod.VodPicScreenshot = field.NewString(tableName, "vod_pic_screenshot")
	_thoVod.VodActor = field.NewString(tableName, "vod_actor")
	_thoVod.VodDirector = field.NewString(tableName, "vod_director")
	_thoVod.VodWriter = field.NewString(tableName, "vod_writer")
	_thoVod.VodBehind = field.NewString(tableName, "vod_behind")
	_thoVod.VodBlurb = field.NewString(tableName, "vod_blurb")
	_thoVod.VodRemarks = field.NewString(tableName, "vod_remarks")
	_thoVod.VodPubdate = field.NewString(tableName, "vod_pubdate")
	_thoVod.VodTotal = field.NewInt32(tableName, "vod_total")
	_thoVod.VodSerial = field.NewString(tableName, "vod_serial")
	_thoVod.VodTv = field.NewString(tableName, "vod_tv")
	_thoVod.VodWeekday = field.NewString(tableName, "vod_weekday")
	_thoVod.VodArea = field.NewString(tableName, "vod_area")
	_thoVod.VodLang = field.NewString(tableName, "vod_lang")
	_thoVod.VodYear = field.NewString(tableName, "vod_year")
	_thoVod.VodVersion = field.NewString(tableName, "vod_version")
	_thoVod.VodState = field.NewString(tableName, "vod_state")
	_thoVod.VodAuthor = field.NewString(tableName, "vod_author")
	_thoVod.VodJumpurl = field.NewString(tableName, "vod_jumpurl")
	_thoVod.VodTpl = field.NewString(tableName, "vod_tpl")
	_thoVod.VodTplPlay = field.NewString(tableName, "vod_tpl_play")
	_thoVod.VodTplDown = field.NewString(tableName, "vod_tpl_down")
	_thoVod.VodIsend = field.NewInt8(tableName, "vod_isend")
	_thoVod.VodLock = field.NewInt8(tableName, "vod_lock")
	_thoVod.VodLevel = field.NewInt8(tableName, "vod_level")
	_thoVod.VodCopyright = field.NewInt8(tableName, "vod_copyright")
	_thoVod.VodPoints = field.NewInt32(tableName, "vod_points")
	_thoVod.VodPointsPlay = field.NewInt32(tableName, "vod_points_play")
	_thoVod.VodPointsDown = field.NewInt32(tableName, "vod_points_down")
	_thoVod.VodHits = field.NewInt32(tableName, "vod_hits")
	_thoVod.VodHitsDay = field.NewInt32(tableName, "vod_hits_day")
	_thoVod.VodHitsWeek = field.NewInt32(tableName, "vod_hits_week")
	_thoVod.VodHitsMonth = field.NewInt32(tableName, "vod_hits_month")
	_thoVod.VodDuration = field.NewString(tableName, "vod_duration")
	_thoVod.VodUp = field.NewInt32(tableName, "vod_up")
	_thoVod.VodDown = field.NewInt32(tableName, "vod_down")
	_thoVod.VodScore = field.NewFloat64(tableName, "vod_score")
	_thoVod.VodScoreAll = field.NewInt32(tableName, "vod_score_all")
	_thoVod.VodScoreNum = field.NewInt32(tableName, "vod_score_num")
	_thoVod.VodTime = field.NewInt32(tableName, "vod_time")
	_thoVod.VodTimeAdd = field.NewInt32(tableName, "vod_time_add")
	_thoVod.VodTimeHits = field.NewInt32(tableName, "vod_time_hits")
	_thoVod.VodTimeMake = field.NewInt32(tableName, "vod_time_make")
	_thoVod.VodTrysee = field.NewInt32(tableName, "vod_trysee")
	_thoVod.VodDoubanID = field.NewInt32(tableName, "vod_douban_id")
	_thoVod.VodDoubanScore = field.NewFloat64(tableName, "vod_douban_score")
	_thoVod.VodReurl = field.NewString(tableName, "vod_reurl")
	_thoVod.VodRelVod = field.NewString(tableName, "vod_rel_vod")
	_thoVod.VodRelArt = field.NewString(tableName, "vod_rel_art")
	_thoVod.VodPwd = field.NewString(tableName, "vod_pwd")
	_thoVod.VodPwdURL = field.NewString(tableName, "vod_pwd_url")
	_thoVod.VodPwdPlay = field.NewString(tableName, "vod_pwd_play")
	_thoVod.VodPwdPlayURL = field.NewString(tableName, "vod_pwd_play_url")
	_thoVod.VodPwdDown = field.NewString(tableName, "vod_pwd_down")
	_thoVod.VodPwdDownURL = field.NewString(tableName, "vod_pwd_down_url")
	_thoVod.VodContent = field.NewString(tableName, "vod_content")
	_thoVod.VodPlayFrom = field.NewString(tableName, "vod_play_from")
	_thoVod.VodPlayServer = field.NewString(tableName, "vod_play_server")
	_thoVod.VodPlayNote = field.NewString(tableName, "vod_play_note")
	_thoVod.VodPlayURL = field.NewString(tableName, "vod_play_url")
	_thoVod.VodDownFrom = field.NewString(tableName, "vod_down_from")
	_thoVod.VodDownServer = field.NewString(tableName, "vod_down_server")
	_thoVod.VodDownNote = field.NewString(tableName, "vod_down_note")
	_thoVod.VodDownURL = field.NewString(tableName, "vod_down_url")
	_thoVod.VodPlot = field.NewInt8(tableName, "vod_plot")
	_thoVod.VodPlotName = field.NewString(tableName, "vod_plot_name")
	_thoVod.VodPlotDetail = field.NewString(tableName, "vod_plot_detail")

	_thoVod.fillFieldMap()

	return _thoVod
}

type thoVod struct {
	thoVodDo

	ALL              field.Asterisk
	VodID            field.Int32
	TypeID           field.Int32
	TypeId1          field.Int32
	GroupID          field.Int32
	VodName          field.String
	VodSub           field.String
	VodEn            field.String
	VodStatus        field.Int8
	VodLetter        field.String
	VodColor         field.String
	VodTag           field.String
	VodClass         field.String
	VodPic           field.String
	VodPicThumb      field.String
	VodPicSlide      field.String
	VodPicScreenshot field.String
	VodActor         field.String
	VodDirector      field.String
	VodWriter        field.String
	VodBehind        field.String
	VodBlurb         field.String
	VodRemarks       field.String
	VodPubdate       field.String
	VodTotal         field.Int32
	VodSerial        field.String
	VodTv            field.String
	VodWeekday       field.String
	VodArea          field.String
	VodLang          field.String
	VodYear          field.String
	VodVersion       field.String
	VodState         field.String
	VodAuthor        field.String
	VodJumpurl       field.String
	VodTpl           field.String
	VodTplPlay       field.String
	VodTplDown       field.String
	VodIsend         field.Int8
	VodLock          field.Int8
	VodLevel         field.Int8
	VodCopyright     field.Int8
	VodPoints        field.Int32
	VodPointsPlay    field.Int32
	VodPointsDown    field.Int32
	VodHits          field.Int32
	VodHitsDay       field.Int32
	VodHitsWeek      field.Int32
	VodHitsMonth     field.Int32
	VodDuration      field.String
	VodUp            field.Int32
	VodDown          field.Int32
	VodScore         field.Float64
	VodScoreAll      field.Int32
	VodScoreNum      field.Int32
	VodTime          field.Int32
	VodTimeAdd       field.Int32
	VodTimeHits      field.Int32
	VodTimeMake      field.Int32
	VodTrysee        field.Int32
	VodDoubanID      field.Int32
	VodDoubanScore   field.Float64
	VodReurl         field.String
	VodRelVod        field.String
	VodRelArt        field.String
	VodPwd           field.String
	VodPwdURL        field.String
	VodPwdPlay       field.String
	VodPwdPlayURL    field.String
	VodPwdDown       field.String
	VodPwdDownURL    field.String
	VodContent       field.String
	VodPlayFrom      field.String
	VodPlayServer    field.String
	VodPlayNote      field.String
	VodPlayURL       field.String
	VodDownFrom      field.String
	VodDownServer    field.String
	VodDownNote      field.String
	VodDownURL       field.String
	VodPlot          field.Int8
	VodPlotName      field.String
	VodPlotDetail    field.String

	fieldMap map[string]field.Expr
}

func (t thoVod) Table(newTableName string) *thoVod {
	t.thoVodDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thoVod) As(alias string) *thoVod {
	t.thoVodDo.DO = *(t.thoVodDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thoVod) updateTableName(table string) *thoVod {
	t.ALL = field.NewAsterisk(table)
	t.VodID = field.NewInt32(table, "vod_id")
	t.TypeID = field.NewInt32(table, "type_id")
	t.TypeId1 = field.NewInt32(table, "type_id_1")
	t.GroupID = field.NewInt32(table, "group_id")
	t.VodName = field.NewString(table, "vod_name")
	t.VodSub = field.NewString(table, "vod_sub")
	t.VodEn = field.NewString(table, "vod_en")
	t.VodStatus = field.NewInt8(table, "vod_status")
	t.VodLetter = field.NewString(table, "vod_letter")
	t.VodColor = field.NewString(table, "vod_color")
	t.VodTag = field.NewString(table, "vod_tag")
	t.VodClass = field.NewString(table, "vod_class")
	t.VodPic = field.NewString(table, "vod_pic")
	t.VodPicThumb = field.NewString(table, "vod_pic_thumb")
	t.VodPicSlide = field.NewString(table, "vod_pic_slide")
	t.VodPicScreenshot = field.NewString(table, "vod_pic_screenshot")
	t.VodActor = field.NewString(table, "vod_actor")
	t.VodDirector = field.NewString(table, "vod_director")
	t.VodWriter = field.NewString(table, "vod_writer")
	t.VodBehind = field.NewString(table, "vod_behind")
	t.VodBlurb = field.NewString(table, "vod_blurb")
	t.VodRemarks = field.NewString(table, "vod_remarks")
	t.VodPubdate = field.NewString(table, "vod_pubdate")
	t.VodTotal = field.NewInt32(table, "vod_total")
	t.VodSerial = field.NewString(table, "vod_serial")
	t.VodTv = field.NewString(table, "vod_tv")
	t.VodWeekday = field.NewString(table, "vod_weekday")
	t.VodArea = field.NewString(table, "vod_area")
	t.VodLang = field.NewString(table, "vod_lang")
	t.VodYear = field.NewString(table, "vod_year")
	t.VodVersion = field.NewString(table, "vod_version")
	t.VodState = field.NewString(table, "vod_state")
	t.VodAuthor = field.NewString(table, "vod_author")
	t.VodJumpurl = field.NewString(table, "vod_jumpurl")
	t.VodTpl = field.NewString(table, "vod_tpl")
	t.VodTplPlay = field.NewString(table, "vod_tpl_play")
	t.VodTplDown = field.NewString(table, "vod_tpl_down")
	t.VodIsend = field.NewInt8(table, "vod_isend")
	t.VodLock = field.NewInt8(table, "vod_lock")
	t.VodLevel = field.NewInt8(table, "vod_level")
	t.VodCopyright = field.NewInt8(table, "vod_copyright")
	t.VodPoints = field.NewInt32(table, "vod_points")
	t.VodPointsPlay = field.NewInt32(table, "vod_points_play")
	t.VodPointsDown = field.NewInt32(table, "vod_points_down")
	t.VodHits = field.NewInt32(table, "vod_hits")
	t.VodHitsDay = field.NewInt32(table, "vod_hits_day")
	t.VodHitsWeek = field.NewInt32(table, "vod_hits_week")
	t.VodHitsMonth = field.NewInt32(table, "vod_hits_month")
	t.VodDuration = field.NewString(table, "vod_duration")
	t.VodUp = field.NewInt32(table, "vod_up")
	t.VodDown = field.NewInt32(table, "vod_down")
	t.VodScore = field.NewFloat64(table, "vod_score")
	t.VodScoreAll = field.NewInt32(table, "vod_score_all")
	t.VodScoreNum = field.NewInt32(table, "vod_score_num")
	t.VodTime = field.NewInt32(table, "vod_time")
	t.VodTimeAdd = field.NewInt32(table, "vod_time_add")
	t.VodTimeHits = field.NewInt32(table, "vod_time_hits")
	t.VodTimeMake = field.NewInt32(table, "vod_time_make")
	t.VodTrysee = field.NewInt32(table, "vod_trysee")
	t.VodDoubanID = field.NewInt32(table, "vod_douban_id")
	t.VodDoubanScore = field.NewFloat64(table, "vod_douban_score")
	t.VodReurl = field.NewString(table, "vod_reurl")
	t.VodRelVod = field.NewString(table, "vod_rel_vod")
	t.VodRelArt = field.NewString(table, "vod_rel_art")
	t.VodPwd = field.NewString(table, "vod_pwd")
	t.VodPwdURL = field.NewString(table, "vod_pwd_url")
	t.VodPwdPlay = field.NewString(table, "vod_pwd_play")
	t.VodPwdPlayURL = field.NewString(table, "vod_pwd_play_url")
	t.VodPwdDown = field.NewString(table, "vod_pwd_down")
	t.VodPwdDownURL = field.NewString(table, "vod_pwd_down_url")
	t.VodContent = field.NewString(table, "vod_content")
	t.VodPlayFrom = field.NewString(table, "vod_play_from")
	t.VodPlayServer = field.NewString(table, "vod_play_server")
	t.VodPlayNote = field.NewString(table, "vod_play_note")
	t.VodPlayURL = field.NewString(table, "vod_play_url")
	t.VodDownFrom = field.NewString(table, "vod_down_from")
	t.VodDownServer = field.NewString(table, "vod_down_server")
	t.VodDownNote = field.NewString(table, "vod_down_note")
	t.VodDownURL = field.NewString(table, "vod_down_url")
	t.VodPlot = field.NewInt8(table, "vod_plot")
	t.VodPlotName = field.NewString(table, "vod_plot_name")
	t.VodPlotDetail = field.NewString(table, "vod_plot_detail")

	t.fillFieldMap()

	return t
}

func (t *thoVod) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thoVod) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 82)
	t.fieldMap["vod_id"] = t.VodID
	t.fieldMap["type_id"] = t.TypeID
	t.fieldMap["type_id_1"] = t.TypeId1
	t.fieldMap["group_id"] = t.GroupID
	t.fieldMap["vod_name"] = t.VodName
	t.fieldMap["vod_sub"] = t.VodSub
	t.fieldMap["vod_en"] = t.VodEn
	t.fieldMap["vod_status"] = t.VodStatus
	t.fieldMap["vod_letter"] = t.VodLetter
	t.fieldMap["vod_color"] = t.VodColor
	t.fieldMap["vod_tag"] = t.VodTag
	t.fieldMap["vod_class"] = t.VodClass
	t.fieldMap["vod_pic"] = t.VodPic
	t.fieldMap["vod_pic_thumb"] = t.VodPicThumb
	t.fieldMap["vod_pic_slide"] = t.VodPicSlide
	t.fieldMap["vod_pic_screenshot"] = t.VodPicScreenshot
	t.fieldMap["vod_actor"] = t.VodActor
	t.fieldMap["vod_director"] = t.VodDirector
	t.fieldMap["vod_writer"] = t.VodWriter
	t.fieldMap["vod_behind"] = t.VodBehind
	t.fieldMap["vod_blurb"] = t.VodBlurb
	t.fieldMap["vod_remarks"] = t.VodRemarks
	t.fieldMap["vod_pubdate"] = t.VodPubdate
	t.fieldMap["vod_total"] = t.VodTotal
	t.fieldMap["vod_serial"] = t.VodSerial
	t.fieldMap["vod_tv"] = t.VodTv
	t.fieldMap["vod_weekday"] = t.VodWeekday
	t.fieldMap["vod_area"] = t.VodArea
	t.fieldMap["vod_lang"] = t.VodLang
	t.fieldMap["vod_year"] = t.VodYear
	t.fieldMap["vod_version"] = t.VodVersion
	t.fieldMap["vod_state"] = t.VodState
	t.fieldMap["vod_author"] = t.VodAuthor
	t.fieldMap["vod_jumpurl"] = t.VodJumpurl
	t.fieldMap["vod_tpl"] = t.VodTpl
	t.fieldMap["vod_tpl_play"] = t.VodTplPlay
	t.fieldMap["vod_tpl_down"] = t.VodTplDown
	t.fieldMap["vod_isend"] = t.VodIsend
	t.fieldMap["vod_lock"] = t.VodLock
	t.fieldMap["vod_level"] = t.VodLevel
	t.fieldMap["vod_copyright"] = t.VodCopyright
	t.fieldMap["vod_points"] = t.VodPoints
	t.fieldMap["vod_points_play"] = t.VodPointsPlay
	t.fieldMap["vod_points_down"] = t.VodPointsDown
	t.fieldMap["vod_hits"] = t.VodHits
	t.fieldMap["vod_hits_day"] = t.VodHitsDay
	t.fieldMap["vod_hits_week"] = t.VodHitsWeek
	t.fieldMap["vod_hits_month"] = t.VodHitsMonth
	t.fieldMap["vod_duration"] = t.VodDuration
	t.fieldMap["vod_up"] = t.VodUp
	t.fieldMap["vod_down"] = t.VodDown
	t.fieldMap["vod_score"] = t.VodScore
	t.fieldMap["vod_score_all"] = t.VodScoreAll
	t.fieldMap["vod_score_num"] = t.VodScoreNum
	t.fieldMap["vod_time"] = t.VodTime
	t.fieldMap["vod_time_add"] = t.VodTimeAdd
	t.fieldMap["vod_time_hits"] = t.VodTimeHits
	t.fieldMap["vod_time_make"] = t.VodTimeMake
	t.fieldMap["vod_trysee"] = t.VodTrysee
	t.fieldMap["vod_douban_id"] = t.VodDoubanID
	t.fieldMap["vod_douban_score"] = t.VodDoubanScore
	t.fieldMap["vod_reurl"] = t.VodReurl
	t.fieldMap["vod_rel_vod"] = t.VodRelVod
	t.fieldMap["vod_rel_art"] = t.VodRelArt
	t.fieldMap["vod_pwd"] = t.VodPwd
	t.fieldMap["vod_pwd_url"] = t.VodPwdURL
	t.fieldMap["vod_pwd_play"] = t.VodPwdPlay
	t.fieldMap["vod_pwd_play_url"] = t.VodPwdPlayURL
	t.fieldMap["vod_pwd_down"] = t.VodPwdDown
	t.fieldMap["vod_pwd_down_url"] = t.VodPwdDownURL
	t.fieldMap["vod_content"] = t.VodContent
	t.fieldMap["vod_play_from"] = t.VodPlayFrom
	t.fieldMap["vod_play_server"] = t.VodPlayServer
	t.fieldMap["vod_play_note"] = t.VodPlayNote
	t.fieldMap["vod_play_url"] = t.VodPlayURL
	t.fieldMap["vod_down_from"] = t.VodDownFrom
	t.fieldMap["vod_down_server"] = t.VodDownServer
	t.fieldMap["vod_down_note"] = t.VodDownNote
	t.fieldMap["vod_down_url"] = t.VodDownURL
	t.fieldMap["vod_plot"] = t.VodPlot
	t.fieldMap["vod_plot_name"] = t.VodPlotName
	t.fieldMap["vod_plot_detail"] = t.VodPlotDetail
}

func (t thoVod) clone(db *gorm.DB) thoVod {
	t.thoVodDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thoVod) replaceDB(db *gorm.DB) thoVod {
	t.thoVodDo.ReplaceDB(db)
	return t
}

type thoVodDo struct{ gen.DO }

type IThoVodDo interface {
	gen.SubQuery
	Debug() IThoVodDo
	WithContext(ctx context.Context) IThoVodDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThoVodDo
	WriteDB() IThoVodDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThoVodDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThoVodDo
	Not(conds ...gen.Condition) IThoVodDo
	Or(conds ...gen.Condition) IThoVodDo
	Select(conds ...field.Expr) IThoVodDo
	Where(conds ...gen.Condition) IThoVodDo
	Order(conds ...field.Expr) IThoVodDo
	Distinct(cols ...field.Expr) IThoVodDo
	Omit(cols ...field.Expr) IThoVodDo
	Join(table schema.Tabler, on ...field.Expr) IThoVodDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThoVodDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThoVodDo
	Group(cols ...field.Expr) IThoVodDo
	Having(conds ...gen.Condition) IThoVodDo
	Limit(limit int) IThoVodDo
	Offset(offset int) IThoVodDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThoVodDo
	Unscoped() IThoVodDo
	Create(values ...*model.ThoVod) error
	CreateInBatches(values []*model.ThoVod, batchSize int) error
	Save(values ...*model.ThoVod) error
	First() (*model.ThoVod, error)
	Take() (*model.ThoVod, error)
	Last() (*model.ThoVod, error)
	Find() ([]*model.ThoVod, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoVod, err error)
	FindInBatches(result *[]*model.ThoVod, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThoVod) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThoVodDo
	Assign(attrs ...field.AssignExpr) IThoVodDo
	Joins(fields ...field.RelationField) IThoVodDo
	Preload(fields ...field.RelationField) IThoVodDo
	FirstOrInit() (*model.ThoVod, error)
	FirstOrCreate() (*model.ThoVod, error)
	FindByPage(offset int, limit int) (result []*model.ThoVod, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThoVodDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thoVodDo) Debug() IThoVodDo {
	return t.withDO(t.DO.Debug())
}

func (t thoVodDo) WithContext(ctx context.Context) IThoVodDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thoVodDo) ReadDB() IThoVodDo {
	return t.Clauses(dbresolver.Read)
}

func (t thoVodDo) WriteDB() IThoVodDo {
	return t.Clauses(dbresolver.Write)
}

func (t thoVodDo) Session(config *gorm.Session) IThoVodDo {
	return t.withDO(t.DO.Session(config))
}

func (t thoVodDo) Clauses(conds ...clause.Expression) IThoVodDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thoVodDo) Returning(value interface{}, columns ...string) IThoVodDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thoVodDo) Not(conds ...gen.Condition) IThoVodDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thoVodDo) Or(conds ...gen.Condition) IThoVodDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thoVodDo) Select(conds ...field.Expr) IThoVodDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thoVodDo) Where(conds ...gen.Condition) IThoVodDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thoVodDo) Order(conds ...field.Expr) IThoVodDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thoVodDo) Distinct(cols ...field.Expr) IThoVodDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thoVodDo) Omit(cols ...field.Expr) IThoVodDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thoVodDo) Join(table schema.Tabler, on ...field.Expr) IThoVodDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thoVodDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThoVodDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thoVodDo) RightJoin(table schema.Tabler, on ...field.Expr) IThoVodDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thoVodDo) Group(cols ...field.Expr) IThoVodDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thoVodDo) Having(conds ...gen.Condition) IThoVodDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thoVodDo) Limit(limit int) IThoVodDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thoVodDo) Offset(offset int) IThoVodDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thoVodDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThoVodDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thoVodDo) Unscoped() IThoVodDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thoVodDo) Create(values ...*model.ThoVod) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thoVodDo) CreateInBatches(values []*model.ThoVod, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thoVodDo) Save(values ...*model.ThoVod) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thoVodDo) First() (*model.ThoVod, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoVod), nil
	}
}

func (t thoVodDo) Take() (*model.ThoVod, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoVod), nil
	}
}

func (t thoVodDo) Last() (*model.ThoVod, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoVod), nil
	}
}

func (t thoVodDo) Find() ([]*model.ThoVod, error) {
	result, err := t.DO.Find()
	return result.([]*model.ThoVod), err
}

func (t thoVodDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoVod, err error) {
	buf := make([]*model.ThoVod, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thoVodDo) FindInBatches(result *[]*model.ThoVod, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thoVodDo) Attrs(attrs ...field.AssignExpr) IThoVodDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thoVodDo) Assign(attrs ...field.AssignExpr) IThoVodDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thoVodDo) Joins(fields ...field.RelationField) IThoVodDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thoVodDo) Preload(fields ...field.RelationField) IThoVodDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thoVodDo) FirstOrInit() (*model.ThoVod, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoVod), nil
	}
}

func (t thoVodDo) FirstOrCreate() (*model.ThoVod, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoVod), nil
	}
}

func (t thoVodDo) FindByPage(offset int, limit int) (result []*model.ThoVod, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thoVodDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thoVodDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thoVodDo) Delete(models ...*model.ThoVod) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thoVodDo) withDO(do gen.Dao) *thoVodDo {
	t.DO = *do.(*gen.DO)
	return t
}
