// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/wilsonce/rabbitmv_bot/dao/model"
)

func newThoGbook(db *gorm.DB, opts ...gen.DOOption) thoGbook {
	_thoGbook := thoGbook{}

	_thoGbook.thoGbookDo.UseDB(db, opts...)
	_thoGbook.thoGbookDo.UseModel(&model.ThoGbook{})

	tableName := _thoGbook.thoGbookDo.TableName()
	_thoGbook.ALL = field.NewAsterisk(tableName)
	_thoGbook.GbookID = field.NewInt32(tableName, "gbook_id")
	_thoGbook.GbookRid = field.NewInt32(tableName, "gbook_rid")
	_thoGbook.UserID = field.NewInt32(tableName, "user_id")
	_thoGbook.GbookStatus = field.NewInt8(tableName, "gbook_status")
	_thoGbook.GbookName = field.NewString(tableName, "gbook_name")
	_thoGbook.GbookIP = field.NewInt32(tableName, "gbook_ip")
	_thoGbook.GbookTime = field.NewInt32(tableName, "gbook_time")
	_thoGbook.GbookReplyTime = field.NewInt32(tableName, "gbook_reply_time")
	_thoGbook.GbookContent = field.NewString(tableName, "gbook_content")
	_thoGbook.GbookReply = field.NewString(tableName, "gbook_reply")

	_thoGbook.fillFieldMap()

	return _thoGbook
}

type thoGbook struct {
	thoGbookDo

	ALL            field.Asterisk
	GbookID        field.Int32
	GbookRid       field.Int32
	UserID         field.Int32
	GbookStatus    field.Int8
	GbookName      field.String
	GbookIP        field.Int32
	GbookTime      field.Int32
	GbookReplyTime field.Int32
	GbookContent   field.String
	GbookReply     field.String

	fieldMap map[string]field.Expr
}

func (t thoGbook) Table(newTableName string) *thoGbook {
	t.thoGbookDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thoGbook) As(alias string) *thoGbook {
	t.thoGbookDo.DO = *(t.thoGbookDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thoGbook) updateTableName(table string) *thoGbook {
	t.ALL = field.NewAsterisk(table)
	t.GbookID = field.NewInt32(table, "gbook_id")
	t.GbookRid = field.NewInt32(table, "gbook_rid")
	t.UserID = field.NewInt32(table, "user_id")
	t.GbookStatus = field.NewInt8(table, "gbook_status")
	t.GbookName = field.NewString(table, "gbook_name")
	t.GbookIP = field.NewInt32(table, "gbook_ip")
	t.GbookTime = field.NewInt32(table, "gbook_time")
	t.GbookReplyTime = field.NewInt32(table, "gbook_reply_time")
	t.GbookContent = field.NewString(table, "gbook_content")
	t.GbookReply = field.NewString(table, "gbook_reply")

	t.fillFieldMap()

	return t
}

func (t *thoGbook) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thoGbook) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 10)
	t.fieldMap["gbook_id"] = t.GbookID
	t.fieldMap["gbook_rid"] = t.GbookRid
	t.fieldMap["user_id"] = t.UserID
	t.fieldMap["gbook_status"] = t.GbookStatus
	t.fieldMap["gbook_name"] = t.GbookName
	t.fieldMap["gbook_ip"] = t.GbookIP
	t.fieldMap["gbook_time"] = t.GbookTime
	t.fieldMap["gbook_reply_time"] = t.GbookReplyTime
	t.fieldMap["gbook_content"] = t.GbookContent
	t.fieldMap["gbook_reply"] = t.GbookReply
}

func (t thoGbook) clone(db *gorm.DB) thoGbook {
	t.thoGbookDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thoGbook) replaceDB(db *gorm.DB) thoGbook {
	t.thoGbookDo.ReplaceDB(db)
	return t
}

type thoGbookDo struct{ gen.DO }

type IThoGbookDo interface {
	gen.SubQuery
	Debug() IThoGbookDo
	WithContext(ctx context.Context) IThoGbookDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThoGbookDo
	WriteDB() IThoGbookDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThoGbookDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThoGbookDo
	Not(conds ...gen.Condition) IThoGbookDo
	Or(conds ...gen.Condition) IThoGbookDo
	Select(conds ...field.Expr) IThoGbookDo
	Where(conds ...gen.Condition) IThoGbookDo
	Order(conds ...field.Expr) IThoGbookDo
	Distinct(cols ...field.Expr) IThoGbookDo
	Omit(cols ...field.Expr) IThoGbookDo
	Join(table schema.Tabler, on ...field.Expr) IThoGbookDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThoGbookDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThoGbookDo
	Group(cols ...field.Expr) IThoGbookDo
	Having(conds ...gen.Condition) IThoGbookDo
	Limit(limit int) IThoGbookDo
	Offset(offset int) IThoGbookDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThoGbookDo
	Unscoped() IThoGbookDo
	Create(values ...*model.ThoGbook) error
	CreateInBatches(values []*model.ThoGbook, batchSize int) error
	Save(values ...*model.ThoGbook) error
	First() (*model.ThoGbook, error)
	Take() (*model.ThoGbook, error)
	Last() (*model.ThoGbook, error)
	Find() ([]*model.ThoGbook, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoGbook, err error)
	FindInBatches(result *[]*model.ThoGbook, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThoGbook) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThoGbookDo
	Assign(attrs ...field.AssignExpr) IThoGbookDo
	Joins(fields ...field.RelationField) IThoGbookDo
	Preload(fields ...field.RelationField) IThoGbookDo
	FirstOrInit() (*model.ThoGbook, error)
	FirstOrCreate() (*model.ThoGbook, error)
	FindByPage(offset int, limit int) (result []*model.ThoGbook, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThoGbookDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thoGbookDo) Debug() IThoGbookDo {
	return t.withDO(t.DO.Debug())
}

func (t thoGbookDo) WithContext(ctx context.Context) IThoGbookDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thoGbookDo) ReadDB() IThoGbookDo {
	return t.Clauses(dbresolver.Read)
}

func (t thoGbookDo) WriteDB() IThoGbookDo {
	return t.Clauses(dbresolver.Write)
}

func (t thoGbookDo) Session(config *gorm.Session) IThoGbookDo {
	return t.withDO(t.DO.Session(config))
}

func (t thoGbookDo) Clauses(conds ...clause.Expression) IThoGbookDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thoGbookDo) Returning(value interface{}, columns ...string) IThoGbookDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thoGbookDo) Not(conds ...gen.Condition) IThoGbookDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thoGbookDo) Or(conds ...gen.Condition) IThoGbookDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thoGbookDo) Select(conds ...field.Expr) IThoGbookDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thoGbookDo) Where(conds ...gen.Condition) IThoGbookDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thoGbookDo) Order(conds ...field.Expr) IThoGbookDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thoGbookDo) Distinct(cols ...field.Expr) IThoGbookDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thoGbookDo) Omit(cols ...field.Expr) IThoGbookDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thoGbookDo) Join(table schema.Tabler, on ...field.Expr) IThoGbookDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thoGbookDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThoGbookDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thoGbookDo) RightJoin(table schema.Tabler, on ...field.Expr) IThoGbookDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thoGbookDo) Group(cols ...field.Expr) IThoGbookDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thoGbookDo) Having(conds ...gen.Condition) IThoGbookDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thoGbookDo) Limit(limit int) IThoGbookDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thoGbookDo) Offset(offset int) IThoGbookDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thoGbookDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThoGbookDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thoGbookDo) Unscoped() IThoGbookDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thoGbookDo) Create(values ...*model.ThoGbook) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thoGbookDo) CreateInBatches(values []*model.ThoGbook, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thoGbookDo) Save(values ...*model.ThoGbook) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thoGbookDo) First() (*model.ThoGbook, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoGbook), nil
	}
}

func (t thoGbookDo) Take() (*model.ThoGbook, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoGbook), nil
	}
}

func (t thoGbookDo) Last() (*model.ThoGbook, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoGbook), nil
	}
}

func (t thoGbookDo) Find() ([]*model.ThoGbook, error) {
	result, err := t.DO.Find()
	return result.([]*model.ThoGbook), err
}

func (t thoGbookDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoGbook, err error) {
	buf := make([]*model.ThoGbook, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thoGbookDo) FindInBatches(result *[]*model.ThoGbook, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thoGbookDo) Attrs(attrs ...field.AssignExpr) IThoGbookDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thoGbookDo) Assign(attrs ...field.AssignExpr) IThoGbookDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thoGbookDo) Joins(fields ...field.RelationField) IThoGbookDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thoGbookDo) Preload(fields ...field.RelationField) IThoGbookDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thoGbookDo) FirstOrInit() (*model.ThoGbook, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoGbook), nil
	}
}

func (t thoGbookDo) FirstOrCreate() (*model.ThoGbook, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoGbook), nil
	}
}

func (t thoGbookDo) FindByPage(offset int, limit int) (result []*model.ThoGbook, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thoGbookDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thoGbookDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thoGbookDo) Delete(models ...*model.ThoGbook) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thoGbookDo) withDO(do gen.Dao) *thoGbookDo {
	t.DO = *do.(*gen.DO)
	return t
}
