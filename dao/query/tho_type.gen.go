// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/wilsonce/rabbitmv_bot/dao/model"
)

func newThoType(db *gorm.DB, opts ...gen.DOOption) thoType {
	_thoType := thoType{}

	_thoType.thoTypeDo.UseDB(db, opts...)
	_thoType.thoTypeDo.UseModel(&model.ThoType{})

	tableName := _thoType.thoTypeDo.TableName()
	_thoType.ALL = field.NewAsterisk(tableName)
	_thoType.TypeID = field.NewInt32(tableName, "type_id")
	_thoType.TypeName = field.NewString(tableName, "type_name")
	_thoType.TypeEn = field.NewString(tableName, "type_en")
	_thoType.TypeSort = field.NewInt32(tableName, "type_sort")
	_thoType.TypeMid = field.NewInt32(tableName, "type_mid")
	_thoType.TypePid = field.NewInt32(tableName, "type_pid")
	_thoType.TypeStatus = field.NewInt8(tableName, "type_status")
	_thoType.TypeTpl = field.NewString(tableName, "type_tpl")
	_thoType.TypeTplList = field.NewString(tableName, "type_tpl_list")
	_thoType.TypeTplDetail = field.NewString(tableName, "type_tpl_detail")
	_thoType.TypeTplPlay = field.NewString(tableName, "type_tpl_play")
	_thoType.TypeTplDown = field.NewString(tableName, "type_tpl_down")
	_thoType.TypeKey = field.NewString(tableName, "type_key")
	_thoType.TypeDes = field.NewString(tableName, "type_des")
	_thoType.TypeTitle = field.NewString(tableName, "type_title")
	_thoType.TypeUnion = field.NewString(tableName, "type_union")
	_thoType.TypeExtend = field.NewString(tableName, "type_extend")
	_thoType.TypeLogo = field.NewString(tableName, "type_logo")
	_thoType.TypePic = field.NewString(tableName, "type_pic")
	_thoType.TypeJumpurl = field.NewString(tableName, "type_jumpurl")

	_thoType.fillFieldMap()

	return _thoType
}

type thoType struct {
	thoTypeDo

	ALL           field.Asterisk
	TypeID        field.Int32
	TypeName      field.String
	TypeEn        field.String
	TypeSort      field.Int32
	TypeMid       field.Int32
	TypePid       field.Int32
	TypeStatus    field.Int8
	TypeTpl       field.String
	TypeTplList   field.String
	TypeTplDetail field.String
	TypeTplPlay   field.String
	TypeTplDown   field.String
	TypeKey       field.String
	TypeDes       field.String
	TypeTitle     field.String
	TypeUnion     field.String
	TypeExtend    field.String
	TypeLogo      field.String
	TypePic       field.String
	TypeJumpurl   field.String

	fieldMap map[string]field.Expr
}

func (t thoType) Table(newTableName string) *thoType {
	t.thoTypeDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thoType) As(alias string) *thoType {
	t.thoTypeDo.DO = *(t.thoTypeDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thoType) updateTableName(table string) *thoType {
	t.ALL = field.NewAsterisk(table)
	t.TypeID = field.NewInt32(table, "type_id")
	t.TypeName = field.NewString(table, "type_name")
	t.TypeEn = field.NewString(table, "type_en")
	t.TypeSort = field.NewInt32(table, "type_sort")
	t.TypeMid = field.NewInt32(table, "type_mid")
	t.TypePid = field.NewInt32(table, "type_pid")
	t.TypeStatus = field.NewInt8(table, "type_status")
	t.TypeTpl = field.NewString(table, "type_tpl")
	t.TypeTplList = field.NewString(table, "type_tpl_list")
	t.TypeTplDetail = field.NewString(table, "type_tpl_detail")
	t.TypeTplPlay = field.NewString(table, "type_tpl_play")
	t.TypeTplDown = field.NewString(table, "type_tpl_down")
	t.TypeKey = field.NewString(table, "type_key")
	t.TypeDes = field.NewString(table, "type_des")
	t.TypeTitle = field.NewString(table, "type_title")
	t.TypeUnion = field.NewString(table, "type_union")
	t.TypeExtend = field.NewString(table, "type_extend")
	t.TypeLogo = field.NewString(table, "type_logo")
	t.TypePic = field.NewString(table, "type_pic")
	t.TypeJumpurl = field.NewString(table, "type_jumpurl")

	t.fillFieldMap()

	return t
}

func (t *thoType) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thoType) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 20)
	t.fieldMap["type_id"] = t.TypeID
	t.fieldMap["type_name"] = t.TypeName
	t.fieldMap["type_en"] = t.TypeEn
	t.fieldMap["type_sort"] = t.TypeSort
	t.fieldMap["type_mid"] = t.TypeMid
	t.fieldMap["type_pid"] = t.TypePid
	t.fieldMap["type_status"] = t.TypeStatus
	t.fieldMap["type_tpl"] = t.TypeTpl
	t.fieldMap["type_tpl_list"] = t.TypeTplList
	t.fieldMap["type_tpl_detail"] = t.TypeTplDetail
	t.fieldMap["type_tpl_play"] = t.TypeTplPlay
	t.fieldMap["type_tpl_down"] = t.TypeTplDown
	t.fieldMap["type_key"] = t.TypeKey
	t.fieldMap["type_des"] = t.TypeDes
	t.fieldMap["type_title"] = t.TypeTitle
	t.fieldMap["type_union"] = t.TypeUnion
	t.fieldMap["type_extend"] = t.TypeExtend
	t.fieldMap["type_logo"] = t.TypeLogo
	t.fieldMap["type_pic"] = t.TypePic
	t.fieldMap["type_jumpurl"] = t.TypeJumpurl
}

func (t thoType) clone(db *gorm.DB) thoType {
	t.thoTypeDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thoType) replaceDB(db *gorm.DB) thoType {
	t.thoTypeDo.ReplaceDB(db)
	return t
}

type thoTypeDo struct{ gen.DO }

type IThoTypeDo interface {
	gen.SubQuery
	Debug() IThoTypeDo
	WithContext(ctx context.Context) IThoTypeDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThoTypeDo
	WriteDB() IThoTypeDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThoTypeDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThoTypeDo
	Not(conds ...gen.Condition) IThoTypeDo
	Or(conds ...gen.Condition) IThoTypeDo
	Select(conds ...field.Expr) IThoTypeDo
	Where(conds ...gen.Condition) IThoTypeDo
	Order(conds ...field.Expr) IThoTypeDo
	Distinct(cols ...field.Expr) IThoTypeDo
	Omit(cols ...field.Expr) IThoTypeDo
	Join(table schema.Tabler, on ...field.Expr) IThoTypeDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThoTypeDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThoTypeDo
	Group(cols ...field.Expr) IThoTypeDo
	Having(conds ...gen.Condition) IThoTypeDo
	Limit(limit int) IThoTypeDo
	Offset(offset int) IThoTypeDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThoTypeDo
	Unscoped() IThoTypeDo
	Create(values ...*model.ThoType) error
	CreateInBatches(values []*model.ThoType, batchSize int) error
	Save(values ...*model.ThoType) error
	First() (*model.ThoType, error)
	Take() (*model.ThoType, error)
	Last() (*model.ThoType, error)
	Find() ([]*model.ThoType, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoType, err error)
	FindInBatches(result *[]*model.ThoType, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThoType) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThoTypeDo
	Assign(attrs ...field.AssignExpr) IThoTypeDo
	Joins(fields ...field.RelationField) IThoTypeDo
	Preload(fields ...field.RelationField) IThoTypeDo
	FirstOrInit() (*model.ThoType, error)
	FirstOrCreate() (*model.ThoType, error)
	FindByPage(offset int, limit int) (result []*model.ThoType, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThoTypeDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thoTypeDo) Debug() IThoTypeDo {
	return t.withDO(t.DO.Debug())
}

func (t thoTypeDo) WithContext(ctx context.Context) IThoTypeDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thoTypeDo) ReadDB() IThoTypeDo {
	return t.Clauses(dbresolver.Read)
}

func (t thoTypeDo) WriteDB() IThoTypeDo {
	return t.Clauses(dbresolver.Write)
}

func (t thoTypeDo) Session(config *gorm.Session) IThoTypeDo {
	return t.withDO(t.DO.Session(config))
}

func (t thoTypeDo) Clauses(conds ...clause.Expression) IThoTypeDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thoTypeDo) Returning(value interface{}, columns ...string) IThoTypeDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thoTypeDo) Not(conds ...gen.Condition) IThoTypeDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thoTypeDo) Or(conds ...gen.Condition) IThoTypeDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thoTypeDo) Select(conds ...field.Expr) IThoTypeDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thoTypeDo) Where(conds ...gen.Condition) IThoTypeDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thoTypeDo) Order(conds ...field.Expr) IThoTypeDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thoTypeDo) Distinct(cols ...field.Expr) IThoTypeDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thoTypeDo) Omit(cols ...field.Expr) IThoTypeDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thoTypeDo) Join(table schema.Tabler, on ...field.Expr) IThoTypeDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thoTypeDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThoTypeDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thoTypeDo) RightJoin(table schema.Tabler, on ...field.Expr) IThoTypeDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thoTypeDo) Group(cols ...field.Expr) IThoTypeDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thoTypeDo) Having(conds ...gen.Condition) IThoTypeDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thoTypeDo) Limit(limit int) IThoTypeDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thoTypeDo) Offset(offset int) IThoTypeDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thoTypeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThoTypeDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thoTypeDo) Unscoped() IThoTypeDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thoTypeDo) Create(values ...*model.ThoType) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thoTypeDo) CreateInBatches(values []*model.ThoType, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thoTypeDo) Save(values ...*model.ThoType) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thoTypeDo) First() (*model.ThoType, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoType), nil
	}
}

func (t thoTypeDo) Take() (*model.ThoType, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoType), nil
	}
}

func (t thoTypeDo) Last() (*model.ThoType, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoType), nil
	}
}

func (t thoTypeDo) Find() ([]*model.ThoType, error) {
	result, err := t.DO.Find()
	return result.([]*model.ThoType), err
}

func (t thoTypeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoType, err error) {
	buf := make([]*model.ThoType, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thoTypeDo) FindInBatches(result *[]*model.ThoType, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thoTypeDo) Attrs(attrs ...field.AssignExpr) IThoTypeDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thoTypeDo) Assign(attrs ...field.AssignExpr) IThoTypeDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thoTypeDo) Joins(fields ...field.RelationField) IThoTypeDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thoTypeDo) Preload(fields ...field.RelationField) IThoTypeDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thoTypeDo) FirstOrInit() (*model.ThoType, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoType), nil
	}
}

func (t thoTypeDo) FirstOrCreate() (*model.ThoType, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoType), nil
	}
}

func (t thoTypeDo) FindByPage(offset int, limit int) (result []*model.ThoType, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thoTypeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thoTypeDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thoTypeDo) Delete(models ...*model.ThoType) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thoTypeDo) withDO(do gen.Dao) *thoTypeDo {
	t.DO = *do.(*gen.DO)
	return t
}
