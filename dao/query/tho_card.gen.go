// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/wilsonce/rabbitmv_bot/dao/model"
)

func newThoCard(db *gorm.DB, opts ...gen.DOOption) thoCard {
	_thoCard := thoCard{}

	_thoCard.thoCardDo.UseDB(db, opts...)
	_thoCard.thoCardDo.UseModel(&model.ThoCard{})

	tableName := _thoCard.thoCardDo.TableName()
	_thoCard.ALL = field.NewAsterisk(tableName)
	_thoCard.CardID = field.NewInt32(tableName, "card_id")
	_thoCard.CardNo = field.NewString(tableName, "card_no")
	_thoCard.CardPwd = field.NewString(tableName, "card_pwd")
	_thoCard.CardMoney = field.NewInt32(tableName, "card_money")
	_thoCard.CardPoints = field.NewInt32(tableName, "card_points")
	_thoCard.CardUseStatus = field.NewInt8(tableName, "card_use_status")
	_thoCard.CardSaleStatus = field.NewInt8(tableName, "card_sale_status")
	_thoCard.UserID = field.NewInt32(tableName, "user_id")
	_thoCard.CardAddTime = field.NewInt32(tableName, "card_add_time")
	_thoCard.CardUseTime = field.NewInt32(tableName, "card_use_time")

	_thoCard.fillFieldMap()

	return _thoCard
}

type thoCard struct {
	thoCardDo

	ALL            field.Asterisk
	CardID         field.Int32
	CardNo         field.String
	CardPwd        field.String
	CardMoney      field.Int32
	CardPoints     field.Int32
	CardUseStatus  field.Int8
	CardSaleStatus field.Int8
	UserID         field.Int32
	CardAddTime    field.Int32
	CardUseTime    field.Int32

	fieldMap map[string]field.Expr
}

func (t thoCard) Table(newTableName string) *thoCard {
	t.thoCardDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thoCard) As(alias string) *thoCard {
	t.thoCardDo.DO = *(t.thoCardDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thoCard) updateTableName(table string) *thoCard {
	t.ALL = field.NewAsterisk(table)
	t.CardID = field.NewInt32(table, "card_id")
	t.CardNo = field.NewString(table, "card_no")
	t.CardPwd = field.NewString(table, "card_pwd")
	t.CardMoney = field.NewInt32(table, "card_money")
	t.CardPoints = field.NewInt32(table, "card_points")
	t.CardUseStatus = field.NewInt8(table, "card_use_status")
	t.CardSaleStatus = field.NewInt8(table, "card_sale_status")
	t.UserID = field.NewInt32(table, "user_id")
	t.CardAddTime = field.NewInt32(table, "card_add_time")
	t.CardUseTime = field.NewInt32(table, "card_use_time")

	t.fillFieldMap()

	return t
}

func (t *thoCard) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thoCard) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 10)
	t.fieldMap["card_id"] = t.CardID
	t.fieldMap["card_no"] = t.CardNo
	t.fieldMap["card_pwd"] = t.CardPwd
	t.fieldMap["card_money"] = t.CardMoney
	t.fieldMap["card_points"] = t.CardPoints
	t.fieldMap["card_use_status"] = t.CardUseStatus
	t.fieldMap["card_sale_status"] = t.CardSaleStatus
	t.fieldMap["user_id"] = t.UserID
	t.fieldMap["card_add_time"] = t.CardAddTime
	t.fieldMap["card_use_time"] = t.CardUseTime
}

func (t thoCard) clone(db *gorm.DB) thoCard {
	t.thoCardDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thoCard) replaceDB(db *gorm.DB) thoCard {
	t.thoCardDo.ReplaceDB(db)
	return t
}

type thoCardDo struct{ gen.DO }

type IThoCardDo interface {
	gen.SubQuery
	Debug() IThoCardDo
	WithContext(ctx context.Context) IThoCardDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThoCardDo
	WriteDB() IThoCardDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThoCardDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThoCardDo
	Not(conds ...gen.Condition) IThoCardDo
	Or(conds ...gen.Condition) IThoCardDo
	Select(conds ...field.Expr) IThoCardDo
	Where(conds ...gen.Condition) IThoCardDo
	Order(conds ...field.Expr) IThoCardDo
	Distinct(cols ...field.Expr) IThoCardDo
	Omit(cols ...field.Expr) IThoCardDo
	Join(table schema.Tabler, on ...field.Expr) IThoCardDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThoCardDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThoCardDo
	Group(cols ...field.Expr) IThoCardDo
	Having(conds ...gen.Condition) IThoCardDo
	Limit(limit int) IThoCardDo
	Offset(offset int) IThoCardDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThoCardDo
	Unscoped() IThoCardDo
	Create(values ...*model.ThoCard) error
	CreateInBatches(values []*model.ThoCard, batchSize int) error
	Save(values ...*model.ThoCard) error
	First() (*model.ThoCard, error)
	Take() (*model.ThoCard, error)
	Last() (*model.ThoCard, error)
	Find() ([]*model.ThoCard, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoCard, err error)
	FindInBatches(result *[]*model.ThoCard, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThoCard) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThoCardDo
	Assign(attrs ...field.AssignExpr) IThoCardDo
	Joins(fields ...field.RelationField) IThoCardDo
	Preload(fields ...field.RelationField) IThoCardDo
	FirstOrInit() (*model.ThoCard, error)
	FirstOrCreate() (*model.ThoCard, error)
	FindByPage(offset int, limit int) (result []*model.ThoCard, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThoCardDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thoCardDo) Debug() IThoCardDo {
	return t.withDO(t.DO.Debug())
}

func (t thoCardDo) WithContext(ctx context.Context) IThoCardDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thoCardDo) ReadDB() IThoCardDo {
	return t.Clauses(dbresolver.Read)
}

func (t thoCardDo) WriteDB() IThoCardDo {
	return t.Clauses(dbresolver.Write)
}

func (t thoCardDo) Session(config *gorm.Session) IThoCardDo {
	return t.withDO(t.DO.Session(config))
}

func (t thoCardDo) Clauses(conds ...clause.Expression) IThoCardDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thoCardDo) Returning(value interface{}, columns ...string) IThoCardDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thoCardDo) Not(conds ...gen.Condition) IThoCardDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thoCardDo) Or(conds ...gen.Condition) IThoCardDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thoCardDo) Select(conds ...field.Expr) IThoCardDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thoCardDo) Where(conds ...gen.Condition) IThoCardDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thoCardDo) Order(conds ...field.Expr) IThoCardDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thoCardDo) Distinct(cols ...field.Expr) IThoCardDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thoCardDo) Omit(cols ...field.Expr) IThoCardDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thoCardDo) Join(table schema.Tabler, on ...field.Expr) IThoCardDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thoCardDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThoCardDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thoCardDo) RightJoin(table schema.Tabler, on ...field.Expr) IThoCardDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thoCardDo) Group(cols ...field.Expr) IThoCardDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thoCardDo) Having(conds ...gen.Condition) IThoCardDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thoCardDo) Limit(limit int) IThoCardDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thoCardDo) Offset(offset int) IThoCardDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thoCardDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThoCardDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thoCardDo) Unscoped() IThoCardDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thoCardDo) Create(values ...*model.ThoCard) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thoCardDo) CreateInBatches(values []*model.ThoCard, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thoCardDo) Save(values ...*model.ThoCard) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thoCardDo) First() (*model.ThoCard, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoCard), nil
	}
}

func (t thoCardDo) Take() (*model.ThoCard, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoCard), nil
	}
}

func (t thoCardDo) Last() (*model.ThoCard, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoCard), nil
	}
}

func (t thoCardDo) Find() ([]*model.ThoCard, error) {
	result, err := t.DO.Find()
	return result.([]*model.ThoCard), err
}

func (t thoCardDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoCard, err error) {
	buf := make([]*model.ThoCard, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thoCardDo) FindInBatches(result *[]*model.ThoCard, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thoCardDo) Attrs(attrs ...field.AssignExpr) IThoCardDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thoCardDo) Assign(attrs ...field.AssignExpr) IThoCardDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thoCardDo) Joins(fields ...field.RelationField) IThoCardDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thoCardDo) Preload(fields ...field.RelationField) IThoCardDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thoCardDo) FirstOrInit() (*model.ThoCard, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoCard), nil
	}
}

func (t thoCardDo) FirstOrCreate() (*model.ThoCard, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoCard), nil
	}
}

func (t thoCardDo) FindByPage(offset int, limit int) (result []*model.ThoCard, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thoCardDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thoCardDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thoCardDo) Delete(models ...*model.ThoCard) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thoCardDo) withDO(do gen.Dao) *thoCardDo {
	t.DO = *do.(*gen.DO)
	return t
}
