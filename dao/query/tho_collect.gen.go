// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/wilsonce/rabbitmv_bot/dao/model"
)

func newThoCollect(db *gorm.DB, opts ...gen.DOOption) thoCollect {
	_thoCollect := thoCollect{}

	_thoCollect.thoCollectDo.UseDB(db, opts...)
	_thoCollect.thoCollectDo.UseModel(&model.ThoCollect{})

	tableName := _thoCollect.thoCollectDo.TableName()
	_thoCollect.ALL = field.NewAsterisk(tableName)
	_thoCollect.CollectID = field.NewInt32(tableName, "collect_id")
	_thoCollect.CollectName = field.NewString(tableName, "collect_name")
	_thoCollect.CollectURL = field.NewString(tableName, "collect_url")
	_thoCollect.CollectType = field.NewInt8(tableName, "collect_type")
	_thoCollect.CollectMid = field.NewInt8(tableName, "collect_mid")
	_thoCollect.CollectAppid = field.NewString(tableName, "collect_appid")
	_thoCollect.CollectAppkey = field.NewString(tableName, "collect_appkey")
	_thoCollect.CollectParam = field.NewString(tableName, "collect_param")
	_thoCollect.CollectFilter = field.NewInt8(tableName, "collect_filter")
	_thoCollect.CollectFilterFrom = field.NewString(tableName, "collect_filter_from")
	_thoCollect.CollectFilterYear = field.NewString(tableName, "collect_filter_year")
	_thoCollect.CollectOpt = field.NewInt8(tableName, "collect_opt")
	_thoCollect.CollectSyncPicOpt = field.NewInt8(tableName, "collect_sync_pic_opt")

	_thoCollect.fillFieldMap()

	return _thoCollect
}

type thoCollect struct {
	thoCollectDo

	ALL               field.Asterisk
	CollectID         field.Int32
	CollectName       field.String
	CollectURL        field.String
	CollectType       field.Int8
	CollectMid        field.Int8
	CollectAppid      field.String
	CollectAppkey     field.String
	CollectParam      field.String
	CollectFilter     field.Int8
	CollectFilterFrom field.String
	CollectFilterYear field.String // 采集时，过滤年份
	CollectOpt        field.Int8
	CollectSyncPicOpt field.Int8 // 同步图片选项，0-跟随全局，1-开启，2-关闭

	fieldMap map[string]field.Expr
}

func (t thoCollect) Table(newTableName string) *thoCollect {
	t.thoCollectDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thoCollect) As(alias string) *thoCollect {
	t.thoCollectDo.DO = *(t.thoCollectDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thoCollect) updateTableName(table string) *thoCollect {
	t.ALL = field.NewAsterisk(table)
	t.CollectID = field.NewInt32(table, "collect_id")
	t.CollectName = field.NewString(table, "collect_name")
	t.CollectURL = field.NewString(table, "collect_url")
	t.CollectType = field.NewInt8(table, "collect_type")
	t.CollectMid = field.NewInt8(table, "collect_mid")
	t.CollectAppid = field.NewString(table, "collect_appid")
	t.CollectAppkey = field.NewString(table, "collect_appkey")
	t.CollectParam = field.NewString(table, "collect_param")
	t.CollectFilter = field.NewInt8(table, "collect_filter")
	t.CollectFilterFrom = field.NewString(table, "collect_filter_from")
	t.CollectFilterYear = field.NewString(table, "collect_filter_year")
	t.CollectOpt = field.NewInt8(table, "collect_opt")
	t.CollectSyncPicOpt = field.NewInt8(table, "collect_sync_pic_opt")

	t.fillFieldMap()

	return t
}

func (t *thoCollect) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thoCollect) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 13)
	t.fieldMap["collect_id"] = t.CollectID
	t.fieldMap["collect_name"] = t.CollectName
	t.fieldMap["collect_url"] = t.CollectURL
	t.fieldMap["collect_type"] = t.CollectType
	t.fieldMap["collect_mid"] = t.CollectMid
	t.fieldMap["collect_appid"] = t.CollectAppid
	t.fieldMap["collect_appkey"] = t.CollectAppkey
	t.fieldMap["collect_param"] = t.CollectParam
	t.fieldMap["collect_filter"] = t.CollectFilter
	t.fieldMap["collect_filter_from"] = t.CollectFilterFrom
	t.fieldMap["collect_filter_year"] = t.CollectFilterYear
	t.fieldMap["collect_opt"] = t.CollectOpt
	t.fieldMap["collect_sync_pic_opt"] = t.CollectSyncPicOpt
}

func (t thoCollect) clone(db *gorm.DB) thoCollect {
	t.thoCollectDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thoCollect) replaceDB(db *gorm.DB) thoCollect {
	t.thoCollectDo.ReplaceDB(db)
	return t
}

type thoCollectDo struct{ gen.DO }

type IThoCollectDo interface {
	gen.SubQuery
	Debug() IThoCollectDo
	WithContext(ctx context.Context) IThoCollectDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThoCollectDo
	WriteDB() IThoCollectDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThoCollectDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThoCollectDo
	Not(conds ...gen.Condition) IThoCollectDo
	Or(conds ...gen.Condition) IThoCollectDo
	Select(conds ...field.Expr) IThoCollectDo
	Where(conds ...gen.Condition) IThoCollectDo
	Order(conds ...field.Expr) IThoCollectDo
	Distinct(cols ...field.Expr) IThoCollectDo
	Omit(cols ...field.Expr) IThoCollectDo
	Join(table schema.Tabler, on ...field.Expr) IThoCollectDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThoCollectDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThoCollectDo
	Group(cols ...field.Expr) IThoCollectDo
	Having(conds ...gen.Condition) IThoCollectDo
	Limit(limit int) IThoCollectDo
	Offset(offset int) IThoCollectDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThoCollectDo
	Unscoped() IThoCollectDo
	Create(values ...*model.ThoCollect) error
	CreateInBatches(values []*model.ThoCollect, batchSize int) error
	Save(values ...*model.ThoCollect) error
	First() (*model.ThoCollect, error)
	Take() (*model.ThoCollect, error)
	Last() (*model.ThoCollect, error)
	Find() ([]*model.ThoCollect, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoCollect, err error)
	FindInBatches(result *[]*model.ThoCollect, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThoCollect) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThoCollectDo
	Assign(attrs ...field.AssignExpr) IThoCollectDo
	Joins(fields ...field.RelationField) IThoCollectDo
	Preload(fields ...field.RelationField) IThoCollectDo
	FirstOrInit() (*model.ThoCollect, error)
	FirstOrCreate() (*model.ThoCollect, error)
	FindByPage(offset int, limit int) (result []*model.ThoCollect, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThoCollectDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thoCollectDo) Debug() IThoCollectDo {
	return t.withDO(t.DO.Debug())
}

func (t thoCollectDo) WithContext(ctx context.Context) IThoCollectDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thoCollectDo) ReadDB() IThoCollectDo {
	return t.Clauses(dbresolver.Read)
}

func (t thoCollectDo) WriteDB() IThoCollectDo {
	return t.Clauses(dbresolver.Write)
}

func (t thoCollectDo) Session(config *gorm.Session) IThoCollectDo {
	return t.withDO(t.DO.Session(config))
}

func (t thoCollectDo) Clauses(conds ...clause.Expression) IThoCollectDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thoCollectDo) Returning(value interface{}, columns ...string) IThoCollectDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thoCollectDo) Not(conds ...gen.Condition) IThoCollectDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thoCollectDo) Or(conds ...gen.Condition) IThoCollectDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thoCollectDo) Select(conds ...field.Expr) IThoCollectDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thoCollectDo) Where(conds ...gen.Condition) IThoCollectDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thoCollectDo) Order(conds ...field.Expr) IThoCollectDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thoCollectDo) Distinct(cols ...field.Expr) IThoCollectDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thoCollectDo) Omit(cols ...field.Expr) IThoCollectDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thoCollectDo) Join(table schema.Tabler, on ...field.Expr) IThoCollectDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thoCollectDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThoCollectDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thoCollectDo) RightJoin(table schema.Tabler, on ...field.Expr) IThoCollectDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thoCollectDo) Group(cols ...field.Expr) IThoCollectDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thoCollectDo) Having(conds ...gen.Condition) IThoCollectDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thoCollectDo) Limit(limit int) IThoCollectDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thoCollectDo) Offset(offset int) IThoCollectDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thoCollectDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThoCollectDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thoCollectDo) Unscoped() IThoCollectDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thoCollectDo) Create(values ...*model.ThoCollect) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thoCollectDo) CreateInBatches(values []*model.ThoCollect, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thoCollectDo) Save(values ...*model.ThoCollect) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thoCollectDo) First() (*model.ThoCollect, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoCollect), nil
	}
}

func (t thoCollectDo) Take() (*model.ThoCollect, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoCollect), nil
	}
}

func (t thoCollectDo) Last() (*model.ThoCollect, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoCollect), nil
	}
}

func (t thoCollectDo) Find() ([]*model.ThoCollect, error) {
	result, err := t.DO.Find()
	return result.([]*model.ThoCollect), err
}

func (t thoCollectDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoCollect, err error) {
	buf := make([]*model.ThoCollect, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thoCollectDo) FindInBatches(result *[]*model.ThoCollect, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thoCollectDo) Attrs(attrs ...field.AssignExpr) IThoCollectDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thoCollectDo) Assign(attrs ...field.AssignExpr) IThoCollectDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thoCollectDo) Joins(fields ...field.RelationField) IThoCollectDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thoCollectDo) Preload(fields ...field.RelationField) IThoCollectDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thoCollectDo) FirstOrInit() (*model.ThoCollect, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoCollect), nil
	}
}

func (t thoCollectDo) FirstOrCreate() (*model.ThoCollect, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoCollect), nil
	}
}

func (t thoCollectDo) FindByPage(offset int, limit int) (result []*model.ThoCollect, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thoCollectDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thoCollectDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thoCollectDo) Delete(models ...*model.ThoCollect) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thoCollectDo) withDO(do gen.Dao) *thoCollectDo {
	t.DO = *do.(*gen.DO)
	return t
}
