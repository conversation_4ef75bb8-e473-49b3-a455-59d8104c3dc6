// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/wilsonce/rabbitmv_bot/dao/model"
)

func newThoVisit(db *gorm.DB, opts ...gen.DOOption) thoVisit {
	_thoVisit := thoVisit{}

	_thoVisit.thoVisitDo.UseDB(db, opts...)
	_thoVisit.thoVisitDo.UseModel(&model.ThoVisit{})

	tableName := _thoVisit.thoVisitDo.TableName()
	_thoVisit.ALL = field.NewAsterisk(tableName)
	_thoVisit.VisitID = field.NewInt32(tableName, "visit_id")
	_thoVisit.UserID = field.NewInt32(tableName, "user_id")
	_thoVisit.VisitIP = field.NewInt32(tableName, "visit_ip")
	_thoVisit.VisitLy = field.NewString(tableName, "visit_ly")
	_thoVisit.VisitTime = field.NewInt32(tableName, "visit_time")

	_thoVisit.fillFieldMap()

	return _thoVisit
}

type thoVisit struct {
	thoVisitDo

	ALL       field.Asterisk
	VisitID   field.Int32
	UserID    field.Int32
	VisitIP   field.Int32
	VisitLy   field.String
	VisitTime field.Int32

	fieldMap map[string]field.Expr
}

func (t thoVisit) Table(newTableName string) *thoVisit {
	t.thoVisitDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thoVisit) As(alias string) *thoVisit {
	t.thoVisitDo.DO = *(t.thoVisitDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thoVisit) updateTableName(table string) *thoVisit {
	t.ALL = field.NewAsterisk(table)
	t.VisitID = field.NewInt32(table, "visit_id")
	t.UserID = field.NewInt32(table, "user_id")
	t.VisitIP = field.NewInt32(table, "visit_ip")
	t.VisitLy = field.NewString(table, "visit_ly")
	t.VisitTime = field.NewInt32(table, "visit_time")

	t.fillFieldMap()

	return t
}

func (t *thoVisit) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thoVisit) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 5)
	t.fieldMap["visit_id"] = t.VisitID
	t.fieldMap["user_id"] = t.UserID
	t.fieldMap["visit_ip"] = t.VisitIP
	t.fieldMap["visit_ly"] = t.VisitLy
	t.fieldMap["visit_time"] = t.VisitTime
}

func (t thoVisit) clone(db *gorm.DB) thoVisit {
	t.thoVisitDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thoVisit) replaceDB(db *gorm.DB) thoVisit {
	t.thoVisitDo.ReplaceDB(db)
	return t
}

type thoVisitDo struct{ gen.DO }

type IThoVisitDo interface {
	gen.SubQuery
	Debug() IThoVisitDo
	WithContext(ctx context.Context) IThoVisitDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThoVisitDo
	WriteDB() IThoVisitDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThoVisitDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThoVisitDo
	Not(conds ...gen.Condition) IThoVisitDo
	Or(conds ...gen.Condition) IThoVisitDo
	Select(conds ...field.Expr) IThoVisitDo
	Where(conds ...gen.Condition) IThoVisitDo
	Order(conds ...field.Expr) IThoVisitDo
	Distinct(cols ...field.Expr) IThoVisitDo
	Omit(cols ...field.Expr) IThoVisitDo
	Join(table schema.Tabler, on ...field.Expr) IThoVisitDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThoVisitDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThoVisitDo
	Group(cols ...field.Expr) IThoVisitDo
	Having(conds ...gen.Condition) IThoVisitDo
	Limit(limit int) IThoVisitDo
	Offset(offset int) IThoVisitDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThoVisitDo
	Unscoped() IThoVisitDo
	Create(values ...*model.ThoVisit) error
	CreateInBatches(values []*model.ThoVisit, batchSize int) error
	Save(values ...*model.ThoVisit) error
	First() (*model.ThoVisit, error)
	Take() (*model.ThoVisit, error)
	Last() (*model.ThoVisit, error)
	Find() ([]*model.ThoVisit, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoVisit, err error)
	FindInBatches(result *[]*model.ThoVisit, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThoVisit) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThoVisitDo
	Assign(attrs ...field.AssignExpr) IThoVisitDo
	Joins(fields ...field.RelationField) IThoVisitDo
	Preload(fields ...field.RelationField) IThoVisitDo
	FirstOrInit() (*model.ThoVisit, error)
	FirstOrCreate() (*model.ThoVisit, error)
	FindByPage(offset int, limit int) (result []*model.ThoVisit, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThoVisitDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thoVisitDo) Debug() IThoVisitDo {
	return t.withDO(t.DO.Debug())
}

func (t thoVisitDo) WithContext(ctx context.Context) IThoVisitDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thoVisitDo) ReadDB() IThoVisitDo {
	return t.Clauses(dbresolver.Read)
}

func (t thoVisitDo) WriteDB() IThoVisitDo {
	return t.Clauses(dbresolver.Write)
}

func (t thoVisitDo) Session(config *gorm.Session) IThoVisitDo {
	return t.withDO(t.DO.Session(config))
}

func (t thoVisitDo) Clauses(conds ...clause.Expression) IThoVisitDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thoVisitDo) Returning(value interface{}, columns ...string) IThoVisitDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thoVisitDo) Not(conds ...gen.Condition) IThoVisitDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thoVisitDo) Or(conds ...gen.Condition) IThoVisitDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thoVisitDo) Select(conds ...field.Expr) IThoVisitDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thoVisitDo) Where(conds ...gen.Condition) IThoVisitDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thoVisitDo) Order(conds ...field.Expr) IThoVisitDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thoVisitDo) Distinct(cols ...field.Expr) IThoVisitDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thoVisitDo) Omit(cols ...field.Expr) IThoVisitDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thoVisitDo) Join(table schema.Tabler, on ...field.Expr) IThoVisitDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thoVisitDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThoVisitDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thoVisitDo) RightJoin(table schema.Tabler, on ...field.Expr) IThoVisitDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thoVisitDo) Group(cols ...field.Expr) IThoVisitDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thoVisitDo) Having(conds ...gen.Condition) IThoVisitDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thoVisitDo) Limit(limit int) IThoVisitDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thoVisitDo) Offset(offset int) IThoVisitDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thoVisitDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThoVisitDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thoVisitDo) Unscoped() IThoVisitDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thoVisitDo) Create(values ...*model.ThoVisit) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thoVisitDo) CreateInBatches(values []*model.ThoVisit, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thoVisitDo) Save(values ...*model.ThoVisit) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thoVisitDo) First() (*model.ThoVisit, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoVisit), nil
	}
}

func (t thoVisitDo) Take() (*model.ThoVisit, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoVisit), nil
	}
}

func (t thoVisitDo) Last() (*model.ThoVisit, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoVisit), nil
	}
}

func (t thoVisitDo) Find() ([]*model.ThoVisit, error) {
	result, err := t.DO.Find()
	return result.([]*model.ThoVisit), err
}

func (t thoVisitDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoVisit, err error) {
	buf := make([]*model.ThoVisit, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thoVisitDo) FindInBatches(result *[]*model.ThoVisit, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thoVisitDo) Attrs(attrs ...field.AssignExpr) IThoVisitDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thoVisitDo) Assign(attrs ...field.AssignExpr) IThoVisitDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thoVisitDo) Joins(fields ...field.RelationField) IThoVisitDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thoVisitDo) Preload(fields ...field.RelationField) IThoVisitDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thoVisitDo) FirstOrInit() (*model.ThoVisit, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoVisit), nil
	}
}

func (t thoVisitDo) FirstOrCreate() (*model.ThoVisit, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoVisit), nil
	}
}

func (t thoVisitDo) FindByPage(offset int, limit int) (result []*model.ThoVisit, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thoVisitDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thoVisitDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thoVisitDo) Delete(models ...*model.ThoVisit) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thoVisitDo) withDO(do gen.Dao) *thoVisitDo {
	t.DO = *do.(*gen.DO)
	return t
}
