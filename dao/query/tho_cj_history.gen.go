// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/wilsonce/rabbitmv_bot/dao/model"
)

func newThoCjHistory(db *gorm.DB, opts ...gen.DOOption) thoCjHistory {
	_thoCjHistory := thoCjHistory{}

	_thoCjHistory.thoCjHistoryDo.UseDB(db, opts...)
	_thoCjHistory.thoCjHistoryDo.UseModel(&model.ThoCjHistory{})

	tableName := _thoCjHistory.thoCjHistoryDo.TableName()
	_thoCjHistory.ALL = field.NewAsterisk(tableName)
	_thoCjHistory.Md5 = field.NewString(tableName, "md5")

	_thoCjHistory.fillFieldMap()

	return _thoCjHistory
}

type thoCjHistory struct {
	thoCjHistoryDo

	ALL field.Asterisk
	Md5 field.String

	fieldMap map[string]field.Expr
}

func (t thoCjHistory) Table(newTableName string) *thoCjHistory {
	t.thoCjHistoryDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thoCjHistory) As(alias string) *thoCjHistory {
	t.thoCjHistoryDo.DO = *(t.thoCjHistoryDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thoCjHistory) updateTableName(table string) *thoCjHistory {
	t.ALL = field.NewAsterisk(table)
	t.Md5 = field.NewString(table, "md5")

	t.fillFieldMap()

	return t
}

func (t *thoCjHistory) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thoCjHistory) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 1)
	t.fieldMap["md5"] = t.Md5
}

func (t thoCjHistory) clone(db *gorm.DB) thoCjHistory {
	t.thoCjHistoryDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thoCjHistory) replaceDB(db *gorm.DB) thoCjHistory {
	t.thoCjHistoryDo.ReplaceDB(db)
	return t
}

type thoCjHistoryDo struct{ gen.DO }

type IThoCjHistoryDo interface {
	gen.SubQuery
	Debug() IThoCjHistoryDo
	WithContext(ctx context.Context) IThoCjHistoryDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThoCjHistoryDo
	WriteDB() IThoCjHistoryDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThoCjHistoryDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThoCjHistoryDo
	Not(conds ...gen.Condition) IThoCjHistoryDo
	Or(conds ...gen.Condition) IThoCjHistoryDo
	Select(conds ...field.Expr) IThoCjHistoryDo
	Where(conds ...gen.Condition) IThoCjHistoryDo
	Order(conds ...field.Expr) IThoCjHistoryDo
	Distinct(cols ...field.Expr) IThoCjHistoryDo
	Omit(cols ...field.Expr) IThoCjHistoryDo
	Join(table schema.Tabler, on ...field.Expr) IThoCjHistoryDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThoCjHistoryDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThoCjHistoryDo
	Group(cols ...field.Expr) IThoCjHistoryDo
	Having(conds ...gen.Condition) IThoCjHistoryDo
	Limit(limit int) IThoCjHistoryDo
	Offset(offset int) IThoCjHistoryDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThoCjHistoryDo
	Unscoped() IThoCjHistoryDo
	Create(values ...*model.ThoCjHistory) error
	CreateInBatches(values []*model.ThoCjHistory, batchSize int) error
	Save(values ...*model.ThoCjHistory) error
	First() (*model.ThoCjHistory, error)
	Take() (*model.ThoCjHistory, error)
	Last() (*model.ThoCjHistory, error)
	Find() ([]*model.ThoCjHistory, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoCjHistory, err error)
	FindInBatches(result *[]*model.ThoCjHistory, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThoCjHistory) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThoCjHistoryDo
	Assign(attrs ...field.AssignExpr) IThoCjHistoryDo
	Joins(fields ...field.RelationField) IThoCjHistoryDo
	Preload(fields ...field.RelationField) IThoCjHistoryDo
	FirstOrInit() (*model.ThoCjHistory, error)
	FirstOrCreate() (*model.ThoCjHistory, error)
	FindByPage(offset int, limit int) (result []*model.ThoCjHistory, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThoCjHistoryDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thoCjHistoryDo) Debug() IThoCjHistoryDo {
	return t.withDO(t.DO.Debug())
}

func (t thoCjHistoryDo) WithContext(ctx context.Context) IThoCjHistoryDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thoCjHistoryDo) ReadDB() IThoCjHistoryDo {
	return t.Clauses(dbresolver.Read)
}

func (t thoCjHistoryDo) WriteDB() IThoCjHistoryDo {
	return t.Clauses(dbresolver.Write)
}

func (t thoCjHistoryDo) Session(config *gorm.Session) IThoCjHistoryDo {
	return t.withDO(t.DO.Session(config))
}

func (t thoCjHistoryDo) Clauses(conds ...clause.Expression) IThoCjHistoryDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thoCjHistoryDo) Returning(value interface{}, columns ...string) IThoCjHistoryDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thoCjHistoryDo) Not(conds ...gen.Condition) IThoCjHistoryDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thoCjHistoryDo) Or(conds ...gen.Condition) IThoCjHistoryDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thoCjHistoryDo) Select(conds ...field.Expr) IThoCjHistoryDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thoCjHistoryDo) Where(conds ...gen.Condition) IThoCjHistoryDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thoCjHistoryDo) Order(conds ...field.Expr) IThoCjHistoryDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thoCjHistoryDo) Distinct(cols ...field.Expr) IThoCjHistoryDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thoCjHistoryDo) Omit(cols ...field.Expr) IThoCjHistoryDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thoCjHistoryDo) Join(table schema.Tabler, on ...field.Expr) IThoCjHistoryDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thoCjHistoryDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThoCjHistoryDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thoCjHistoryDo) RightJoin(table schema.Tabler, on ...field.Expr) IThoCjHistoryDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thoCjHistoryDo) Group(cols ...field.Expr) IThoCjHistoryDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thoCjHistoryDo) Having(conds ...gen.Condition) IThoCjHistoryDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thoCjHistoryDo) Limit(limit int) IThoCjHistoryDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thoCjHistoryDo) Offset(offset int) IThoCjHistoryDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thoCjHistoryDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThoCjHistoryDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thoCjHistoryDo) Unscoped() IThoCjHistoryDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thoCjHistoryDo) Create(values ...*model.ThoCjHistory) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thoCjHistoryDo) CreateInBatches(values []*model.ThoCjHistory, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thoCjHistoryDo) Save(values ...*model.ThoCjHistory) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thoCjHistoryDo) First() (*model.ThoCjHistory, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoCjHistory), nil
	}
}

func (t thoCjHistoryDo) Take() (*model.ThoCjHistory, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoCjHistory), nil
	}
}

func (t thoCjHistoryDo) Last() (*model.ThoCjHistory, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoCjHistory), nil
	}
}

func (t thoCjHistoryDo) Find() ([]*model.ThoCjHistory, error) {
	result, err := t.DO.Find()
	return result.([]*model.ThoCjHistory), err
}

func (t thoCjHistoryDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoCjHistory, err error) {
	buf := make([]*model.ThoCjHistory, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thoCjHistoryDo) FindInBatches(result *[]*model.ThoCjHistory, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thoCjHistoryDo) Attrs(attrs ...field.AssignExpr) IThoCjHistoryDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thoCjHistoryDo) Assign(attrs ...field.AssignExpr) IThoCjHistoryDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thoCjHistoryDo) Joins(fields ...field.RelationField) IThoCjHistoryDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thoCjHistoryDo) Preload(fields ...field.RelationField) IThoCjHistoryDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thoCjHistoryDo) FirstOrInit() (*model.ThoCjHistory, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoCjHistory), nil
	}
}

func (t thoCjHistoryDo) FirstOrCreate() (*model.ThoCjHistory, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoCjHistory), nil
	}
}

func (t thoCjHistoryDo) FindByPage(offset int, limit int) (result []*model.ThoCjHistory, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thoCjHistoryDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thoCjHistoryDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thoCjHistoryDo) Delete(models ...*model.ThoCjHistory) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thoCjHistoryDo) withDO(do gen.Dao) *thoCjHistoryDo {
	t.DO = *do.(*gen.DO)
	return t
}
