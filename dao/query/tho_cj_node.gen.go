// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/wilsonce/rabbitmv_bot/dao/model"
)

func newThoCjNode(db *gorm.DB, opts ...gen.DOOption) thoCjNode {
	_thoCjNode := thoCjNode{}

	_thoCjNode.thoCjNodeDo.UseDB(db, opts...)
	_thoCjNode.thoCjNodeDo.UseModel(&model.ThoCjNode{})

	tableName := _thoCjNode.thoCjNodeDo.TableName()
	_thoCjNode.ALL = field.NewAsterisk(tableName)
	_thoCjNode.Nodeid = field.NewInt32(tableName, "nodeid")
	_thoCjNode.Name = field.NewString(tableName, "name")
	_thoCjNode.Lastdate = field.NewInt32(tableName, "lastdate")
	_thoCjNode.Sourcecharset = field.NewString(tableName, "sourcecharset")
	_thoCjNode.Sourcetype = field.NewInt8(tableName, "sourcetype")
	_thoCjNode.Urlpage = field.NewString(tableName, "urlpage")
	_thoCjNode.PagesizeStart = field.NewInt8(tableName, "pagesize_start")
	_thoCjNode.PagesizeEnd = field.NewInt32(tableName, "pagesize_end")
	_thoCjNode.PageBase = field.NewString(tableName, "page_base")
	_thoCjNode.ParNum = field.NewInt8(tableName, "par_num")
	_thoCjNode.URLContain = field.NewString(tableName, "url_contain")
	_thoCjNode.URLExcept = field.NewString(tableName, "url_except")
	_thoCjNode.URLStart = field.NewString(tableName, "url_start")
	_thoCjNode.URLEnd = field.NewString(tableName, "url_end")
	_thoCjNode.TitleRule = field.NewString(tableName, "title_rule")
	_thoCjNode.TitleHTMLRule = field.NewString(tableName, "title_html_rule")
	_thoCjNode.TypeRule = field.NewString(tableName, "type_rule")
	_thoCjNode.TypeHTMLRule = field.NewString(tableName, "type_html_rule")
	_thoCjNode.ContentRule = field.NewString(tableName, "content_rule")
	_thoCjNode.ContentHTMLRule = field.NewString(tableName, "content_html_rule")
	_thoCjNode.ContentPageStart = field.NewString(tableName, "content_page_start")
	_thoCjNode.ContentPageEnd = field.NewString(tableName, "content_page_end")
	_thoCjNode.ContentPageRule = field.NewInt8(tableName, "content_page_rule")
	_thoCjNode.ContentPage = field.NewInt8(tableName, "content_page")
	_thoCjNode.ContentNextpage = field.NewString(tableName, "content_nextpage")
	_thoCjNode.DownAttachment = field.NewInt8(tableName, "down_attachment")
	_thoCjNode.Watermark = field.NewInt8(tableName, "watermark")
	_thoCjNode.CollOrder = field.NewInt8(tableName, "coll_order")
	_thoCjNode.CustomizeConfig = field.NewString(tableName, "customize_config")
	_thoCjNode.ProgramConfig = field.NewString(tableName, "program_config")
	_thoCjNode.Mid = field.NewInt8(tableName, "mid")

	_thoCjNode.fillFieldMap()

	return _thoCjNode
}

type thoCjNode struct {
	thoCjNodeDo

	ALL              field.Asterisk
	Nodeid           field.Int32
	Name             field.String
	Lastdate         field.Int32
	Sourcecharset    field.String
	Sourcetype       field.Int8
	Urlpage          field.String
	PagesizeStart    field.Int8
	PagesizeEnd      field.Int32
	PageBase         field.String
	ParNum           field.Int8
	URLContain       field.String
	URLExcept        field.String
	URLStart         field.String
	URLEnd           field.String
	TitleRule        field.String
	TitleHTMLRule    field.String
	TypeRule         field.String
	TypeHTMLRule     field.String
	ContentRule      field.String
	ContentHTMLRule  field.String
	ContentPageStart field.String
	ContentPageEnd   field.String
	ContentPageRule  field.Int8
	ContentPage      field.Int8
	ContentNextpage  field.String
	DownAttachment   field.Int8
	Watermark        field.Int8
	CollOrder        field.Int8
	CustomizeConfig  field.String
	ProgramConfig    field.String
	Mid              field.Int8

	fieldMap map[string]field.Expr
}

func (t thoCjNode) Table(newTableName string) *thoCjNode {
	t.thoCjNodeDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thoCjNode) As(alias string) *thoCjNode {
	t.thoCjNodeDo.DO = *(t.thoCjNodeDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thoCjNode) updateTableName(table string) *thoCjNode {
	t.ALL = field.NewAsterisk(table)
	t.Nodeid = field.NewInt32(table, "nodeid")
	t.Name = field.NewString(table, "name")
	t.Lastdate = field.NewInt32(table, "lastdate")
	t.Sourcecharset = field.NewString(table, "sourcecharset")
	t.Sourcetype = field.NewInt8(table, "sourcetype")
	t.Urlpage = field.NewString(table, "urlpage")
	t.PagesizeStart = field.NewInt8(table, "pagesize_start")
	t.PagesizeEnd = field.NewInt32(table, "pagesize_end")
	t.PageBase = field.NewString(table, "page_base")
	t.ParNum = field.NewInt8(table, "par_num")
	t.URLContain = field.NewString(table, "url_contain")
	t.URLExcept = field.NewString(table, "url_except")
	t.URLStart = field.NewString(table, "url_start")
	t.URLEnd = field.NewString(table, "url_end")
	t.TitleRule = field.NewString(table, "title_rule")
	t.TitleHTMLRule = field.NewString(table, "title_html_rule")
	t.TypeRule = field.NewString(table, "type_rule")
	t.TypeHTMLRule = field.NewString(table, "type_html_rule")
	t.ContentRule = field.NewString(table, "content_rule")
	t.ContentHTMLRule = field.NewString(table, "content_html_rule")
	t.ContentPageStart = field.NewString(table, "content_page_start")
	t.ContentPageEnd = field.NewString(table, "content_page_end")
	t.ContentPageRule = field.NewInt8(table, "content_page_rule")
	t.ContentPage = field.NewInt8(table, "content_page")
	t.ContentNextpage = field.NewString(table, "content_nextpage")
	t.DownAttachment = field.NewInt8(table, "down_attachment")
	t.Watermark = field.NewInt8(table, "watermark")
	t.CollOrder = field.NewInt8(table, "coll_order")
	t.CustomizeConfig = field.NewString(table, "customize_config")
	t.ProgramConfig = field.NewString(table, "program_config")
	t.Mid = field.NewInt8(table, "mid")

	t.fillFieldMap()

	return t
}

func (t *thoCjNode) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thoCjNode) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 31)
	t.fieldMap["nodeid"] = t.Nodeid
	t.fieldMap["name"] = t.Name
	t.fieldMap["lastdate"] = t.Lastdate
	t.fieldMap["sourcecharset"] = t.Sourcecharset
	t.fieldMap["sourcetype"] = t.Sourcetype
	t.fieldMap["urlpage"] = t.Urlpage
	t.fieldMap["pagesize_start"] = t.PagesizeStart
	t.fieldMap["pagesize_end"] = t.PagesizeEnd
	t.fieldMap["page_base"] = t.PageBase
	t.fieldMap["par_num"] = t.ParNum
	t.fieldMap["url_contain"] = t.URLContain
	t.fieldMap["url_except"] = t.URLExcept
	t.fieldMap["url_start"] = t.URLStart
	t.fieldMap["url_end"] = t.URLEnd
	t.fieldMap["title_rule"] = t.TitleRule
	t.fieldMap["title_html_rule"] = t.TitleHTMLRule
	t.fieldMap["type_rule"] = t.TypeRule
	t.fieldMap["type_html_rule"] = t.TypeHTMLRule
	t.fieldMap["content_rule"] = t.ContentRule
	t.fieldMap["content_html_rule"] = t.ContentHTMLRule
	t.fieldMap["content_page_start"] = t.ContentPageStart
	t.fieldMap["content_page_end"] = t.ContentPageEnd
	t.fieldMap["content_page_rule"] = t.ContentPageRule
	t.fieldMap["content_page"] = t.ContentPage
	t.fieldMap["content_nextpage"] = t.ContentNextpage
	t.fieldMap["down_attachment"] = t.DownAttachment
	t.fieldMap["watermark"] = t.Watermark
	t.fieldMap["coll_order"] = t.CollOrder
	t.fieldMap["customize_config"] = t.CustomizeConfig
	t.fieldMap["program_config"] = t.ProgramConfig
	t.fieldMap["mid"] = t.Mid
}

func (t thoCjNode) clone(db *gorm.DB) thoCjNode {
	t.thoCjNodeDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thoCjNode) replaceDB(db *gorm.DB) thoCjNode {
	t.thoCjNodeDo.ReplaceDB(db)
	return t
}

type thoCjNodeDo struct{ gen.DO }

type IThoCjNodeDo interface {
	gen.SubQuery
	Debug() IThoCjNodeDo
	WithContext(ctx context.Context) IThoCjNodeDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThoCjNodeDo
	WriteDB() IThoCjNodeDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThoCjNodeDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThoCjNodeDo
	Not(conds ...gen.Condition) IThoCjNodeDo
	Or(conds ...gen.Condition) IThoCjNodeDo
	Select(conds ...field.Expr) IThoCjNodeDo
	Where(conds ...gen.Condition) IThoCjNodeDo
	Order(conds ...field.Expr) IThoCjNodeDo
	Distinct(cols ...field.Expr) IThoCjNodeDo
	Omit(cols ...field.Expr) IThoCjNodeDo
	Join(table schema.Tabler, on ...field.Expr) IThoCjNodeDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThoCjNodeDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThoCjNodeDo
	Group(cols ...field.Expr) IThoCjNodeDo
	Having(conds ...gen.Condition) IThoCjNodeDo
	Limit(limit int) IThoCjNodeDo
	Offset(offset int) IThoCjNodeDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThoCjNodeDo
	Unscoped() IThoCjNodeDo
	Create(values ...*model.ThoCjNode) error
	CreateInBatches(values []*model.ThoCjNode, batchSize int) error
	Save(values ...*model.ThoCjNode) error
	First() (*model.ThoCjNode, error)
	Take() (*model.ThoCjNode, error)
	Last() (*model.ThoCjNode, error)
	Find() ([]*model.ThoCjNode, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoCjNode, err error)
	FindInBatches(result *[]*model.ThoCjNode, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThoCjNode) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThoCjNodeDo
	Assign(attrs ...field.AssignExpr) IThoCjNodeDo
	Joins(fields ...field.RelationField) IThoCjNodeDo
	Preload(fields ...field.RelationField) IThoCjNodeDo
	FirstOrInit() (*model.ThoCjNode, error)
	FirstOrCreate() (*model.ThoCjNode, error)
	FindByPage(offset int, limit int) (result []*model.ThoCjNode, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThoCjNodeDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thoCjNodeDo) Debug() IThoCjNodeDo {
	return t.withDO(t.DO.Debug())
}

func (t thoCjNodeDo) WithContext(ctx context.Context) IThoCjNodeDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thoCjNodeDo) ReadDB() IThoCjNodeDo {
	return t.Clauses(dbresolver.Read)
}

func (t thoCjNodeDo) WriteDB() IThoCjNodeDo {
	return t.Clauses(dbresolver.Write)
}

func (t thoCjNodeDo) Session(config *gorm.Session) IThoCjNodeDo {
	return t.withDO(t.DO.Session(config))
}

func (t thoCjNodeDo) Clauses(conds ...clause.Expression) IThoCjNodeDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thoCjNodeDo) Returning(value interface{}, columns ...string) IThoCjNodeDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thoCjNodeDo) Not(conds ...gen.Condition) IThoCjNodeDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thoCjNodeDo) Or(conds ...gen.Condition) IThoCjNodeDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thoCjNodeDo) Select(conds ...field.Expr) IThoCjNodeDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thoCjNodeDo) Where(conds ...gen.Condition) IThoCjNodeDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thoCjNodeDo) Order(conds ...field.Expr) IThoCjNodeDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thoCjNodeDo) Distinct(cols ...field.Expr) IThoCjNodeDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thoCjNodeDo) Omit(cols ...field.Expr) IThoCjNodeDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thoCjNodeDo) Join(table schema.Tabler, on ...field.Expr) IThoCjNodeDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thoCjNodeDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThoCjNodeDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thoCjNodeDo) RightJoin(table schema.Tabler, on ...field.Expr) IThoCjNodeDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thoCjNodeDo) Group(cols ...field.Expr) IThoCjNodeDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thoCjNodeDo) Having(conds ...gen.Condition) IThoCjNodeDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thoCjNodeDo) Limit(limit int) IThoCjNodeDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thoCjNodeDo) Offset(offset int) IThoCjNodeDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thoCjNodeDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThoCjNodeDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thoCjNodeDo) Unscoped() IThoCjNodeDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thoCjNodeDo) Create(values ...*model.ThoCjNode) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thoCjNodeDo) CreateInBatches(values []*model.ThoCjNode, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thoCjNodeDo) Save(values ...*model.ThoCjNode) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thoCjNodeDo) First() (*model.ThoCjNode, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoCjNode), nil
	}
}

func (t thoCjNodeDo) Take() (*model.ThoCjNode, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoCjNode), nil
	}
}

func (t thoCjNodeDo) Last() (*model.ThoCjNode, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoCjNode), nil
	}
}

func (t thoCjNodeDo) Find() ([]*model.ThoCjNode, error) {
	result, err := t.DO.Find()
	return result.([]*model.ThoCjNode), err
}

func (t thoCjNodeDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoCjNode, err error) {
	buf := make([]*model.ThoCjNode, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thoCjNodeDo) FindInBatches(result *[]*model.ThoCjNode, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thoCjNodeDo) Attrs(attrs ...field.AssignExpr) IThoCjNodeDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thoCjNodeDo) Assign(attrs ...field.AssignExpr) IThoCjNodeDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thoCjNodeDo) Joins(fields ...field.RelationField) IThoCjNodeDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thoCjNodeDo) Preload(fields ...field.RelationField) IThoCjNodeDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thoCjNodeDo) FirstOrInit() (*model.ThoCjNode, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoCjNode), nil
	}
}

func (t thoCjNodeDo) FirstOrCreate() (*model.ThoCjNode, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoCjNode), nil
	}
}

func (t thoCjNodeDo) FindByPage(offset int, limit int) (result []*model.ThoCjNode, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thoCjNodeDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thoCjNodeDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thoCjNodeDo) Delete(models ...*model.ThoCjNode) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thoCjNodeDo) withDO(do gen.Dao) *thoCjNodeDo {
	t.DO = *do.(*gen.DO)
	return t
}
