// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/wilsonce/rabbitmv_bot/dao/model"
)

func newThoAdmin(db *gorm.DB, opts ...gen.DOOption) thoAdmin {
	_thoAdmin := thoAdmin{}

	_thoAdmin.thoAdminDo.UseDB(db, opts...)
	_thoAdmin.thoAdminDo.UseModel(&model.ThoAdmin{})

	tableName := _thoAdmin.thoAdminDo.TableName()
	_thoAdmin.ALL = field.NewAsterisk(tableName)
	_thoAdmin.AdminID = field.NewInt32(tableName, "admin_id")
	_thoAdmin.AdminName = field.NewString(tableName, "admin_name")
	_thoAdmin.AdminPwd = field.NewString(tableName, "admin_pwd")
	_thoAdmin.AdminRandom = field.NewString(tableName, "admin_random")
	_thoAdmin.AdminStatus = field.NewInt8(tableName, "admin_status")
	_thoAdmin.AdminAuth = field.NewString(tableName, "admin_auth")
	_thoAdmin.AdminLoginTime = field.NewInt32(tableName, "admin_login_time")
	_thoAdmin.AdminLoginIP = field.NewInt32(tableName, "admin_login_ip")
	_thoAdmin.AdminLoginNum = field.NewInt32(tableName, "admin_login_num")
	_thoAdmin.AdminLastLoginTime = field.NewInt32(tableName, "admin_last_login_time")
	_thoAdmin.AdminLastLoginIP = field.NewInt32(tableName, "admin_last_login_ip")

	_thoAdmin.fillFieldMap()

	return _thoAdmin
}

type thoAdmin struct {
	thoAdminDo

	ALL                field.Asterisk
	AdminID            field.Int32
	AdminName          field.String
	AdminPwd           field.String
	AdminRandom        field.String
	AdminStatus        field.Int8
	AdminAuth          field.String
	AdminLoginTime     field.Int32
	AdminLoginIP       field.Int32
	AdminLoginNum      field.Int32
	AdminLastLoginTime field.Int32
	AdminLastLoginIP   field.Int32

	fieldMap map[string]field.Expr
}

func (t thoAdmin) Table(newTableName string) *thoAdmin {
	t.thoAdminDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thoAdmin) As(alias string) *thoAdmin {
	t.thoAdminDo.DO = *(t.thoAdminDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thoAdmin) updateTableName(table string) *thoAdmin {
	t.ALL = field.NewAsterisk(table)
	t.AdminID = field.NewInt32(table, "admin_id")
	t.AdminName = field.NewString(table, "admin_name")
	t.AdminPwd = field.NewString(table, "admin_pwd")
	t.AdminRandom = field.NewString(table, "admin_random")
	t.AdminStatus = field.NewInt8(table, "admin_status")
	t.AdminAuth = field.NewString(table, "admin_auth")
	t.AdminLoginTime = field.NewInt32(table, "admin_login_time")
	t.AdminLoginIP = field.NewInt32(table, "admin_login_ip")
	t.AdminLoginNum = field.NewInt32(table, "admin_login_num")
	t.AdminLastLoginTime = field.NewInt32(table, "admin_last_login_time")
	t.AdminLastLoginIP = field.NewInt32(table, "admin_last_login_ip")

	t.fillFieldMap()

	return t
}

func (t *thoAdmin) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thoAdmin) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 11)
	t.fieldMap["admin_id"] = t.AdminID
	t.fieldMap["admin_name"] = t.AdminName
	t.fieldMap["admin_pwd"] = t.AdminPwd
	t.fieldMap["admin_random"] = t.AdminRandom
	t.fieldMap["admin_status"] = t.AdminStatus
	t.fieldMap["admin_auth"] = t.AdminAuth
	t.fieldMap["admin_login_time"] = t.AdminLoginTime
	t.fieldMap["admin_login_ip"] = t.AdminLoginIP
	t.fieldMap["admin_login_num"] = t.AdminLoginNum
	t.fieldMap["admin_last_login_time"] = t.AdminLastLoginTime
	t.fieldMap["admin_last_login_ip"] = t.AdminLastLoginIP
}

func (t thoAdmin) clone(db *gorm.DB) thoAdmin {
	t.thoAdminDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thoAdmin) replaceDB(db *gorm.DB) thoAdmin {
	t.thoAdminDo.ReplaceDB(db)
	return t
}

type thoAdminDo struct{ gen.DO }

type IThoAdminDo interface {
	gen.SubQuery
	Debug() IThoAdminDo
	WithContext(ctx context.Context) IThoAdminDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThoAdminDo
	WriteDB() IThoAdminDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThoAdminDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThoAdminDo
	Not(conds ...gen.Condition) IThoAdminDo
	Or(conds ...gen.Condition) IThoAdminDo
	Select(conds ...field.Expr) IThoAdminDo
	Where(conds ...gen.Condition) IThoAdminDo
	Order(conds ...field.Expr) IThoAdminDo
	Distinct(cols ...field.Expr) IThoAdminDo
	Omit(cols ...field.Expr) IThoAdminDo
	Join(table schema.Tabler, on ...field.Expr) IThoAdminDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThoAdminDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThoAdminDo
	Group(cols ...field.Expr) IThoAdminDo
	Having(conds ...gen.Condition) IThoAdminDo
	Limit(limit int) IThoAdminDo
	Offset(offset int) IThoAdminDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThoAdminDo
	Unscoped() IThoAdminDo
	Create(values ...*model.ThoAdmin) error
	CreateInBatches(values []*model.ThoAdmin, batchSize int) error
	Save(values ...*model.ThoAdmin) error
	First() (*model.ThoAdmin, error)
	Take() (*model.ThoAdmin, error)
	Last() (*model.ThoAdmin, error)
	Find() ([]*model.ThoAdmin, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoAdmin, err error)
	FindInBatches(result *[]*model.ThoAdmin, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThoAdmin) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThoAdminDo
	Assign(attrs ...field.AssignExpr) IThoAdminDo
	Joins(fields ...field.RelationField) IThoAdminDo
	Preload(fields ...field.RelationField) IThoAdminDo
	FirstOrInit() (*model.ThoAdmin, error)
	FirstOrCreate() (*model.ThoAdmin, error)
	FindByPage(offset int, limit int) (result []*model.ThoAdmin, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThoAdminDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thoAdminDo) Debug() IThoAdminDo {
	return t.withDO(t.DO.Debug())
}

func (t thoAdminDo) WithContext(ctx context.Context) IThoAdminDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thoAdminDo) ReadDB() IThoAdminDo {
	return t.Clauses(dbresolver.Read)
}

func (t thoAdminDo) WriteDB() IThoAdminDo {
	return t.Clauses(dbresolver.Write)
}

func (t thoAdminDo) Session(config *gorm.Session) IThoAdminDo {
	return t.withDO(t.DO.Session(config))
}

func (t thoAdminDo) Clauses(conds ...clause.Expression) IThoAdminDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thoAdminDo) Returning(value interface{}, columns ...string) IThoAdminDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thoAdminDo) Not(conds ...gen.Condition) IThoAdminDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thoAdminDo) Or(conds ...gen.Condition) IThoAdminDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thoAdminDo) Select(conds ...field.Expr) IThoAdminDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thoAdminDo) Where(conds ...gen.Condition) IThoAdminDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thoAdminDo) Order(conds ...field.Expr) IThoAdminDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thoAdminDo) Distinct(cols ...field.Expr) IThoAdminDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thoAdminDo) Omit(cols ...field.Expr) IThoAdminDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thoAdminDo) Join(table schema.Tabler, on ...field.Expr) IThoAdminDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thoAdminDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThoAdminDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thoAdminDo) RightJoin(table schema.Tabler, on ...field.Expr) IThoAdminDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thoAdminDo) Group(cols ...field.Expr) IThoAdminDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thoAdminDo) Having(conds ...gen.Condition) IThoAdminDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thoAdminDo) Limit(limit int) IThoAdminDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thoAdminDo) Offset(offset int) IThoAdminDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thoAdminDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThoAdminDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thoAdminDo) Unscoped() IThoAdminDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thoAdminDo) Create(values ...*model.ThoAdmin) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thoAdminDo) CreateInBatches(values []*model.ThoAdmin, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thoAdminDo) Save(values ...*model.ThoAdmin) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thoAdminDo) First() (*model.ThoAdmin, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoAdmin), nil
	}
}

func (t thoAdminDo) Take() (*model.ThoAdmin, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoAdmin), nil
	}
}

func (t thoAdminDo) Last() (*model.ThoAdmin, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoAdmin), nil
	}
}

func (t thoAdminDo) Find() ([]*model.ThoAdmin, error) {
	result, err := t.DO.Find()
	return result.([]*model.ThoAdmin), err
}

func (t thoAdminDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoAdmin, err error) {
	buf := make([]*model.ThoAdmin, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thoAdminDo) FindInBatches(result *[]*model.ThoAdmin, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thoAdminDo) Attrs(attrs ...field.AssignExpr) IThoAdminDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thoAdminDo) Assign(attrs ...field.AssignExpr) IThoAdminDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thoAdminDo) Joins(fields ...field.RelationField) IThoAdminDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thoAdminDo) Preload(fields ...field.RelationField) IThoAdminDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thoAdminDo) FirstOrInit() (*model.ThoAdmin, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoAdmin), nil
	}
}

func (t thoAdminDo) FirstOrCreate() (*model.ThoAdmin, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoAdmin), nil
	}
}

func (t thoAdminDo) FindByPage(offset int, limit int) (result []*model.ThoAdmin, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thoAdminDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thoAdminDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thoAdminDo) Delete(models ...*model.ThoAdmin) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thoAdminDo) withDO(do gen.Dao) *thoAdminDo {
	t.DO = *do.(*gen.DO)
	return t
}
