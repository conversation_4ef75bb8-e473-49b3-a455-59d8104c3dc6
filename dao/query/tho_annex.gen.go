// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/wilsonce/rabbitmv_bot/dao/model"
)

func newThoAnnex(db *gorm.DB, opts ...gen.DOOption) thoAnnex {
	_thoAnnex := thoAnnex{}

	_thoAnnex.thoAnnexDo.UseDB(db, opts...)
	_thoAnnex.thoAnnexDo.UseModel(&model.ThoAnnex{})

	tableName := _thoAnnex.thoAnnexDo.TableName()
	_thoAnnex.ALL = field.NewAsterisk(tableName)
	_thoAnnex.AnnexID = field.NewInt32(tableName, "annex_id")
	_thoAnnex.AnnexTime = field.NewInt32(tableName, "annex_time")
	_thoAnnex.AnnexFile = field.NewString(tableName, "annex_file")
	_thoAnnex.AnnexSize = field.NewInt32(tableName, "annex_size")
	_thoAnnex.AnnexType = field.NewString(tableName, "annex_type")

	_thoAnnex.fillFieldMap()

	return _thoAnnex
}

type thoAnnex struct {
	thoAnnexDo

	ALL       field.Asterisk
	AnnexID   field.Int32
	AnnexTime field.Int32
	AnnexFile field.String
	AnnexSize field.Int32
	AnnexType field.String

	fieldMap map[string]field.Expr
}

func (t thoAnnex) Table(newTableName string) *thoAnnex {
	t.thoAnnexDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thoAnnex) As(alias string) *thoAnnex {
	t.thoAnnexDo.DO = *(t.thoAnnexDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thoAnnex) updateTableName(table string) *thoAnnex {
	t.ALL = field.NewAsterisk(table)
	t.AnnexID = field.NewInt32(table, "annex_id")
	t.AnnexTime = field.NewInt32(table, "annex_time")
	t.AnnexFile = field.NewString(table, "annex_file")
	t.AnnexSize = field.NewInt32(table, "annex_size")
	t.AnnexType = field.NewString(table, "annex_type")

	t.fillFieldMap()

	return t
}

func (t *thoAnnex) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thoAnnex) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 5)
	t.fieldMap["annex_id"] = t.AnnexID
	t.fieldMap["annex_time"] = t.AnnexTime
	t.fieldMap["annex_file"] = t.AnnexFile
	t.fieldMap["annex_size"] = t.AnnexSize
	t.fieldMap["annex_type"] = t.AnnexType
}

func (t thoAnnex) clone(db *gorm.DB) thoAnnex {
	t.thoAnnexDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thoAnnex) replaceDB(db *gorm.DB) thoAnnex {
	t.thoAnnexDo.ReplaceDB(db)
	return t
}

type thoAnnexDo struct{ gen.DO }

type IThoAnnexDo interface {
	gen.SubQuery
	Debug() IThoAnnexDo
	WithContext(ctx context.Context) IThoAnnexDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThoAnnexDo
	WriteDB() IThoAnnexDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThoAnnexDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThoAnnexDo
	Not(conds ...gen.Condition) IThoAnnexDo
	Or(conds ...gen.Condition) IThoAnnexDo
	Select(conds ...field.Expr) IThoAnnexDo
	Where(conds ...gen.Condition) IThoAnnexDo
	Order(conds ...field.Expr) IThoAnnexDo
	Distinct(cols ...field.Expr) IThoAnnexDo
	Omit(cols ...field.Expr) IThoAnnexDo
	Join(table schema.Tabler, on ...field.Expr) IThoAnnexDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThoAnnexDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThoAnnexDo
	Group(cols ...field.Expr) IThoAnnexDo
	Having(conds ...gen.Condition) IThoAnnexDo
	Limit(limit int) IThoAnnexDo
	Offset(offset int) IThoAnnexDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThoAnnexDo
	Unscoped() IThoAnnexDo
	Create(values ...*model.ThoAnnex) error
	CreateInBatches(values []*model.ThoAnnex, batchSize int) error
	Save(values ...*model.ThoAnnex) error
	First() (*model.ThoAnnex, error)
	Take() (*model.ThoAnnex, error)
	Last() (*model.ThoAnnex, error)
	Find() ([]*model.ThoAnnex, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoAnnex, err error)
	FindInBatches(result *[]*model.ThoAnnex, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThoAnnex) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThoAnnexDo
	Assign(attrs ...field.AssignExpr) IThoAnnexDo
	Joins(fields ...field.RelationField) IThoAnnexDo
	Preload(fields ...field.RelationField) IThoAnnexDo
	FirstOrInit() (*model.ThoAnnex, error)
	FirstOrCreate() (*model.ThoAnnex, error)
	FindByPage(offset int, limit int) (result []*model.ThoAnnex, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThoAnnexDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thoAnnexDo) Debug() IThoAnnexDo {
	return t.withDO(t.DO.Debug())
}

func (t thoAnnexDo) WithContext(ctx context.Context) IThoAnnexDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thoAnnexDo) ReadDB() IThoAnnexDo {
	return t.Clauses(dbresolver.Read)
}

func (t thoAnnexDo) WriteDB() IThoAnnexDo {
	return t.Clauses(dbresolver.Write)
}

func (t thoAnnexDo) Session(config *gorm.Session) IThoAnnexDo {
	return t.withDO(t.DO.Session(config))
}

func (t thoAnnexDo) Clauses(conds ...clause.Expression) IThoAnnexDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thoAnnexDo) Returning(value interface{}, columns ...string) IThoAnnexDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thoAnnexDo) Not(conds ...gen.Condition) IThoAnnexDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thoAnnexDo) Or(conds ...gen.Condition) IThoAnnexDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thoAnnexDo) Select(conds ...field.Expr) IThoAnnexDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thoAnnexDo) Where(conds ...gen.Condition) IThoAnnexDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thoAnnexDo) Order(conds ...field.Expr) IThoAnnexDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thoAnnexDo) Distinct(cols ...field.Expr) IThoAnnexDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thoAnnexDo) Omit(cols ...field.Expr) IThoAnnexDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thoAnnexDo) Join(table schema.Tabler, on ...field.Expr) IThoAnnexDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thoAnnexDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThoAnnexDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thoAnnexDo) RightJoin(table schema.Tabler, on ...field.Expr) IThoAnnexDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thoAnnexDo) Group(cols ...field.Expr) IThoAnnexDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thoAnnexDo) Having(conds ...gen.Condition) IThoAnnexDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thoAnnexDo) Limit(limit int) IThoAnnexDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thoAnnexDo) Offset(offset int) IThoAnnexDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thoAnnexDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThoAnnexDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thoAnnexDo) Unscoped() IThoAnnexDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thoAnnexDo) Create(values ...*model.ThoAnnex) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thoAnnexDo) CreateInBatches(values []*model.ThoAnnex, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thoAnnexDo) Save(values ...*model.ThoAnnex) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thoAnnexDo) First() (*model.ThoAnnex, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoAnnex), nil
	}
}

func (t thoAnnexDo) Take() (*model.ThoAnnex, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoAnnex), nil
	}
}

func (t thoAnnexDo) Last() (*model.ThoAnnex, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoAnnex), nil
	}
}

func (t thoAnnexDo) Find() ([]*model.ThoAnnex, error) {
	result, err := t.DO.Find()
	return result.([]*model.ThoAnnex), err
}

func (t thoAnnexDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoAnnex, err error) {
	buf := make([]*model.ThoAnnex, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thoAnnexDo) FindInBatches(result *[]*model.ThoAnnex, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thoAnnexDo) Attrs(attrs ...field.AssignExpr) IThoAnnexDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thoAnnexDo) Assign(attrs ...field.AssignExpr) IThoAnnexDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thoAnnexDo) Joins(fields ...field.RelationField) IThoAnnexDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thoAnnexDo) Preload(fields ...field.RelationField) IThoAnnexDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thoAnnexDo) FirstOrInit() (*model.ThoAnnex, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoAnnex), nil
	}
}

func (t thoAnnexDo) FirstOrCreate() (*model.ThoAnnex, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoAnnex), nil
	}
}

func (t thoAnnexDo) FindByPage(offset int, limit int) (result []*model.ThoAnnex, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thoAnnexDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thoAnnexDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thoAnnexDo) Delete(models ...*model.ThoAnnex) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thoAnnexDo) withDO(do gen.Dao) *thoAnnexDo {
	t.DO = *do.(*gen.DO)
	return t
}
