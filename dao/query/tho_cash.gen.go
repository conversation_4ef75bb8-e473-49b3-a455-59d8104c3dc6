// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/wilsonce/rabbitmv_bot/dao/model"
)

func newThoCash(db *gorm.DB, opts ...gen.DOOption) thoCash {
	_thoCash := thoCash{}

	_thoCash.thoCashDo.UseDB(db, opts...)
	_thoCash.thoCashDo.UseModel(&model.ThoCash{})

	tableName := _thoCash.thoCashDo.TableName()
	_thoCash.ALL = field.NewAsterisk(tableName)
	_thoCash.CashID = field.NewInt32(tableName, "cash_id")
	_thoCash.UserID = field.NewInt32(tableName, "user_id")
	_thoCash.CashStatus = field.NewInt8(tableName, "cash_status")
	_thoCash.CashPoints = field.NewInt32(tableName, "cash_points")
	_thoCash.CashMoney = field.NewFloat64(tableName, "cash_money")
	_thoCash.CashBankName = field.NewString(tableName, "cash_bank_name")
	_thoCash.CashBankNo = field.NewString(tableName, "cash_bank_no")
	_thoCash.CashPayeeName = field.NewString(tableName, "cash_payee_name")
	_thoCash.CashTime = field.NewInt32(tableName, "cash_time")
	_thoCash.CashTimeAudit = field.NewInt32(tableName, "cash_time_audit")

	_thoCash.fillFieldMap()

	return _thoCash
}

type thoCash struct {
	thoCashDo

	ALL           field.Asterisk
	CashID        field.Int32
	UserID        field.Int32
	CashStatus    field.Int8
	CashPoints    field.Int32
	CashMoney     field.Float64
	CashBankName  field.String
	CashBankNo    field.String
	CashPayeeName field.String
	CashTime      field.Int32
	CashTimeAudit field.Int32

	fieldMap map[string]field.Expr
}

func (t thoCash) Table(newTableName string) *thoCash {
	t.thoCashDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thoCash) As(alias string) *thoCash {
	t.thoCashDo.DO = *(t.thoCashDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thoCash) updateTableName(table string) *thoCash {
	t.ALL = field.NewAsterisk(table)
	t.CashID = field.NewInt32(table, "cash_id")
	t.UserID = field.NewInt32(table, "user_id")
	t.CashStatus = field.NewInt8(table, "cash_status")
	t.CashPoints = field.NewInt32(table, "cash_points")
	t.CashMoney = field.NewFloat64(table, "cash_money")
	t.CashBankName = field.NewString(table, "cash_bank_name")
	t.CashBankNo = field.NewString(table, "cash_bank_no")
	t.CashPayeeName = field.NewString(table, "cash_payee_name")
	t.CashTime = field.NewInt32(table, "cash_time")
	t.CashTimeAudit = field.NewInt32(table, "cash_time_audit")

	t.fillFieldMap()

	return t
}

func (t *thoCash) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thoCash) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 10)
	t.fieldMap["cash_id"] = t.CashID
	t.fieldMap["user_id"] = t.UserID
	t.fieldMap["cash_status"] = t.CashStatus
	t.fieldMap["cash_points"] = t.CashPoints
	t.fieldMap["cash_money"] = t.CashMoney
	t.fieldMap["cash_bank_name"] = t.CashBankName
	t.fieldMap["cash_bank_no"] = t.CashBankNo
	t.fieldMap["cash_payee_name"] = t.CashPayeeName
	t.fieldMap["cash_time"] = t.CashTime
	t.fieldMap["cash_time_audit"] = t.CashTimeAudit
}

func (t thoCash) clone(db *gorm.DB) thoCash {
	t.thoCashDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thoCash) replaceDB(db *gorm.DB) thoCash {
	t.thoCashDo.ReplaceDB(db)
	return t
}

type thoCashDo struct{ gen.DO }

type IThoCashDo interface {
	gen.SubQuery
	Debug() IThoCashDo
	WithContext(ctx context.Context) IThoCashDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThoCashDo
	WriteDB() IThoCashDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThoCashDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThoCashDo
	Not(conds ...gen.Condition) IThoCashDo
	Or(conds ...gen.Condition) IThoCashDo
	Select(conds ...field.Expr) IThoCashDo
	Where(conds ...gen.Condition) IThoCashDo
	Order(conds ...field.Expr) IThoCashDo
	Distinct(cols ...field.Expr) IThoCashDo
	Omit(cols ...field.Expr) IThoCashDo
	Join(table schema.Tabler, on ...field.Expr) IThoCashDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThoCashDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThoCashDo
	Group(cols ...field.Expr) IThoCashDo
	Having(conds ...gen.Condition) IThoCashDo
	Limit(limit int) IThoCashDo
	Offset(offset int) IThoCashDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThoCashDo
	Unscoped() IThoCashDo
	Create(values ...*model.ThoCash) error
	CreateInBatches(values []*model.ThoCash, batchSize int) error
	Save(values ...*model.ThoCash) error
	First() (*model.ThoCash, error)
	Take() (*model.ThoCash, error)
	Last() (*model.ThoCash, error)
	Find() ([]*model.ThoCash, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoCash, err error)
	FindInBatches(result *[]*model.ThoCash, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThoCash) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThoCashDo
	Assign(attrs ...field.AssignExpr) IThoCashDo
	Joins(fields ...field.RelationField) IThoCashDo
	Preload(fields ...field.RelationField) IThoCashDo
	FirstOrInit() (*model.ThoCash, error)
	FirstOrCreate() (*model.ThoCash, error)
	FindByPage(offset int, limit int) (result []*model.ThoCash, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThoCashDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thoCashDo) Debug() IThoCashDo {
	return t.withDO(t.DO.Debug())
}

func (t thoCashDo) WithContext(ctx context.Context) IThoCashDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thoCashDo) ReadDB() IThoCashDo {
	return t.Clauses(dbresolver.Read)
}

func (t thoCashDo) WriteDB() IThoCashDo {
	return t.Clauses(dbresolver.Write)
}

func (t thoCashDo) Session(config *gorm.Session) IThoCashDo {
	return t.withDO(t.DO.Session(config))
}

func (t thoCashDo) Clauses(conds ...clause.Expression) IThoCashDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thoCashDo) Returning(value interface{}, columns ...string) IThoCashDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thoCashDo) Not(conds ...gen.Condition) IThoCashDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thoCashDo) Or(conds ...gen.Condition) IThoCashDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thoCashDo) Select(conds ...field.Expr) IThoCashDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thoCashDo) Where(conds ...gen.Condition) IThoCashDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thoCashDo) Order(conds ...field.Expr) IThoCashDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thoCashDo) Distinct(cols ...field.Expr) IThoCashDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thoCashDo) Omit(cols ...field.Expr) IThoCashDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thoCashDo) Join(table schema.Tabler, on ...field.Expr) IThoCashDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thoCashDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThoCashDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thoCashDo) RightJoin(table schema.Tabler, on ...field.Expr) IThoCashDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thoCashDo) Group(cols ...field.Expr) IThoCashDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thoCashDo) Having(conds ...gen.Condition) IThoCashDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thoCashDo) Limit(limit int) IThoCashDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thoCashDo) Offset(offset int) IThoCashDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thoCashDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThoCashDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thoCashDo) Unscoped() IThoCashDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thoCashDo) Create(values ...*model.ThoCash) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thoCashDo) CreateInBatches(values []*model.ThoCash, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thoCashDo) Save(values ...*model.ThoCash) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thoCashDo) First() (*model.ThoCash, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoCash), nil
	}
}

func (t thoCashDo) Take() (*model.ThoCash, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoCash), nil
	}
}

func (t thoCashDo) Last() (*model.ThoCash, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoCash), nil
	}
}

func (t thoCashDo) Find() ([]*model.ThoCash, error) {
	result, err := t.DO.Find()
	return result.([]*model.ThoCash), err
}

func (t thoCashDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoCash, err error) {
	buf := make([]*model.ThoCash, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thoCashDo) FindInBatches(result *[]*model.ThoCash, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thoCashDo) Attrs(attrs ...field.AssignExpr) IThoCashDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thoCashDo) Assign(attrs ...field.AssignExpr) IThoCashDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thoCashDo) Joins(fields ...field.RelationField) IThoCashDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thoCashDo) Preload(fields ...field.RelationField) IThoCashDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thoCashDo) FirstOrInit() (*model.ThoCash, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoCash), nil
	}
}

func (t thoCashDo) FirstOrCreate() (*model.ThoCash, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoCash), nil
	}
}

func (t thoCashDo) FindByPage(offset int, limit int) (result []*model.ThoCash, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thoCashDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thoCashDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thoCashDo) Delete(models ...*model.ThoCash) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thoCashDo) withDO(do gen.Dao) *thoCashDo {
	t.DO = *do.(*gen.DO)
	return t
}
