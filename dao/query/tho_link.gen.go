// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.
// Code generated by gorm.io/gen. DO NOT EDIT.

package query

import (
	"context"

	"gorm.io/gorm"
	"gorm.io/gorm/clause"
	"gorm.io/gorm/schema"

	"gorm.io/gen"
	"gorm.io/gen/field"

	"gorm.io/plugin/dbresolver"

	"github.com/wilsonce/rabbitmv_bot/dao/model"
)

func newThoLink(db *gorm.DB, opts ...gen.DOOption) thoLink {
	_thoLink := thoLink{}

	_thoLink.thoLinkDo.UseDB(db, opts...)
	_thoLink.thoLinkDo.UseModel(&model.ThoLink{})

	tableName := _thoLink.thoLinkDo.TableName()
	_thoLink.ALL = field.NewAsterisk(tableName)
	_thoLink.LinkID = field.NewInt32(tableName, "link_id")
	_thoLink.LinkType = field.NewInt8(tableName, "link_type")
	_thoLink.LinkName = field.NewString(tableName, "link_name")
	_thoLink.LinkSort = field.NewInt32(tableName, "link_sort")
	_thoLink.LinkAddTime = field.NewInt32(tableName, "link_add_time")
	_thoLink.LinkTime = field.NewInt32(tableName, "link_time")
	_thoLink.LinkURL = field.NewString(tableName, "link_url")
	_thoLink.LinkLogo = field.NewString(tableName, "link_logo")

	_thoLink.fillFieldMap()

	return _thoLink
}

type thoLink struct {
	thoLinkDo

	ALL         field.Asterisk
	LinkID      field.Int32
	LinkType    field.Int8
	LinkName    field.String
	LinkSort    field.Int32
	LinkAddTime field.Int32
	LinkTime    field.Int32
	LinkURL     field.String
	LinkLogo    field.String

	fieldMap map[string]field.Expr
}

func (t thoLink) Table(newTableName string) *thoLink {
	t.thoLinkDo.UseTable(newTableName)
	return t.updateTableName(newTableName)
}

func (t thoLink) As(alias string) *thoLink {
	t.thoLinkDo.DO = *(t.thoLinkDo.As(alias).(*gen.DO))
	return t.updateTableName(alias)
}

func (t *thoLink) updateTableName(table string) *thoLink {
	t.ALL = field.NewAsterisk(table)
	t.LinkID = field.NewInt32(table, "link_id")
	t.LinkType = field.NewInt8(table, "link_type")
	t.LinkName = field.NewString(table, "link_name")
	t.LinkSort = field.NewInt32(table, "link_sort")
	t.LinkAddTime = field.NewInt32(table, "link_add_time")
	t.LinkTime = field.NewInt32(table, "link_time")
	t.LinkURL = field.NewString(table, "link_url")
	t.LinkLogo = field.NewString(table, "link_logo")

	t.fillFieldMap()

	return t
}

func (t *thoLink) GetFieldByName(fieldName string) (field.OrderExpr, bool) {
	_f, ok := t.fieldMap[fieldName]
	if !ok || _f == nil {
		return nil, false
	}
	_oe, ok := _f.(field.OrderExpr)
	return _oe, ok
}

func (t *thoLink) fillFieldMap() {
	t.fieldMap = make(map[string]field.Expr, 8)
	t.fieldMap["link_id"] = t.LinkID
	t.fieldMap["link_type"] = t.LinkType
	t.fieldMap["link_name"] = t.LinkName
	t.fieldMap["link_sort"] = t.LinkSort
	t.fieldMap["link_add_time"] = t.LinkAddTime
	t.fieldMap["link_time"] = t.LinkTime
	t.fieldMap["link_url"] = t.LinkURL
	t.fieldMap["link_logo"] = t.LinkLogo
}

func (t thoLink) clone(db *gorm.DB) thoLink {
	t.thoLinkDo.ReplaceConnPool(db.Statement.ConnPool)
	return t
}

func (t thoLink) replaceDB(db *gorm.DB) thoLink {
	t.thoLinkDo.ReplaceDB(db)
	return t
}

type thoLinkDo struct{ gen.DO }

type IThoLinkDo interface {
	gen.SubQuery
	Debug() IThoLinkDo
	WithContext(ctx context.Context) IThoLinkDo
	WithResult(fc func(tx gen.Dao)) gen.ResultInfo
	ReplaceDB(db *gorm.DB)
	ReadDB() IThoLinkDo
	WriteDB() IThoLinkDo
	As(alias string) gen.Dao
	Session(config *gorm.Session) IThoLinkDo
	Columns(cols ...field.Expr) gen.Columns
	Clauses(conds ...clause.Expression) IThoLinkDo
	Not(conds ...gen.Condition) IThoLinkDo
	Or(conds ...gen.Condition) IThoLinkDo
	Select(conds ...field.Expr) IThoLinkDo
	Where(conds ...gen.Condition) IThoLinkDo
	Order(conds ...field.Expr) IThoLinkDo
	Distinct(cols ...field.Expr) IThoLinkDo
	Omit(cols ...field.Expr) IThoLinkDo
	Join(table schema.Tabler, on ...field.Expr) IThoLinkDo
	LeftJoin(table schema.Tabler, on ...field.Expr) IThoLinkDo
	RightJoin(table schema.Tabler, on ...field.Expr) IThoLinkDo
	Group(cols ...field.Expr) IThoLinkDo
	Having(conds ...gen.Condition) IThoLinkDo
	Limit(limit int) IThoLinkDo
	Offset(offset int) IThoLinkDo
	Count() (count int64, err error)
	Scopes(funcs ...func(gen.Dao) gen.Dao) IThoLinkDo
	Unscoped() IThoLinkDo
	Create(values ...*model.ThoLink) error
	CreateInBatches(values []*model.ThoLink, batchSize int) error
	Save(values ...*model.ThoLink) error
	First() (*model.ThoLink, error)
	Take() (*model.ThoLink, error)
	Last() (*model.ThoLink, error)
	Find() ([]*model.ThoLink, error)
	FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoLink, err error)
	FindInBatches(result *[]*model.ThoLink, batchSize int, fc func(tx gen.Dao, batch int) error) error
	Pluck(column field.Expr, dest interface{}) error
	Delete(...*model.ThoLink) (info gen.ResultInfo, err error)
	Update(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	Updates(value interface{}) (info gen.ResultInfo, err error)
	UpdateColumn(column field.Expr, value interface{}) (info gen.ResultInfo, err error)
	UpdateColumnSimple(columns ...field.AssignExpr) (info gen.ResultInfo, err error)
	UpdateColumns(value interface{}) (info gen.ResultInfo, err error)
	UpdateFrom(q gen.SubQuery) gen.Dao
	Attrs(attrs ...field.AssignExpr) IThoLinkDo
	Assign(attrs ...field.AssignExpr) IThoLinkDo
	Joins(fields ...field.RelationField) IThoLinkDo
	Preload(fields ...field.RelationField) IThoLinkDo
	FirstOrInit() (*model.ThoLink, error)
	FirstOrCreate() (*model.ThoLink, error)
	FindByPage(offset int, limit int) (result []*model.ThoLink, count int64, err error)
	ScanByPage(result interface{}, offset int, limit int) (count int64, err error)
	Scan(result interface{}) (err error)
	Returning(value interface{}, columns ...string) IThoLinkDo
	UnderlyingDB() *gorm.DB
	schema.Tabler
}

func (t thoLinkDo) Debug() IThoLinkDo {
	return t.withDO(t.DO.Debug())
}

func (t thoLinkDo) WithContext(ctx context.Context) IThoLinkDo {
	return t.withDO(t.DO.WithContext(ctx))
}

func (t thoLinkDo) ReadDB() IThoLinkDo {
	return t.Clauses(dbresolver.Read)
}

func (t thoLinkDo) WriteDB() IThoLinkDo {
	return t.Clauses(dbresolver.Write)
}

func (t thoLinkDo) Session(config *gorm.Session) IThoLinkDo {
	return t.withDO(t.DO.Session(config))
}

func (t thoLinkDo) Clauses(conds ...clause.Expression) IThoLinkDo {
	return t.withDO(t.DO.Clauses(conds...))
}

func (t thoLinkDo) Returning(value interface{}, columns ...string) IThoLinkDo {
	return t.withDO(t.DO.Returning(value, columns...))
}

func (t thoLinkDo) Not(conds ...gen.Condition) IThoLinkDo {
	return t.withDO(t.DO.Not(conds...))
}

func (t thoLinkDo) Or(conds ...gen.Condition) IThoLinkDo {
	return t.withDO(t.DO.Or(conds...))
}

func (t thoLinkDo) Select(conds ...field.Expr) IThoLinkDo {
	return t.withDO(t.DO.Select(conds...))
}

func (t thoLinkDo) Where(conds ...gen.Condition) IThoLinkDo {
	return t.withDO(t.DO.Where(conds...))
}

func (t thoLinkDo) Order(conds ...field.Expr) IThoLinkDo {
	return t.withDO(t.DO.Order(conds...))
}

func (t thoLinkDo) Distinct(cols ...field.Expr) IThoLinkDo {
	return t.withDO(t.DO.Distinct(cols...))
}

func (t thoLinkDo) Omit(cols ...field.Expr) IThoLinkDo {
	return t.withDO(t.DO.Omit(cols...))
}

func (t thoLinkDo) Join(table schema.Tabler, on ...field.Expr) IThoLinkDo {
	return t.withDO(t.DO.Join(table, on...))
}

func (t thoLinkDo) LeftJoin(table schema.Tabler, on ...field.Expr) IThoLinkDo {
	return t.withDO(t.DO.LeftJoin(table, on...))
}

func (t thoLinkDo) RightJoin(table schema.Tabler, on ...field.Expr) IThoLinkDo {
	return t.withDO(t.DO.RightJoin(table, on...))
}

func (t thoLinkDo) Group(cols ...field.Expr) IThoLinkDo {
	return t.withDO(t.DO.Group(cols...))
}

func (t thoLinkDo) Having(conds ...gen.Condition) IThoLinkDo {
	return t.withDO(t.DO.Having(conds...))
}

func (t thoLinkDo) Limit(limit int) IThoLinkDo {
	return t.withDO(t.DO.Limit(limit))
}

func (t thoLinkDo) Offset(offset int) IThoLinkDo {
	return t.withDO(t.DO.Offset(offset))
}

func (t thoLinkDo) Scopes(funcs ...func(gen.Dao) gen.Dao) IThoLinkDo {
	return t.withDO(t.DO.Scopes(funcs...))
}

func (t thoLinkDo) Unscoped() IThoLinkDo {
	return t.withDO(t.DO.Unscoped())
}

func (t thoLinkDo) Create(values ...*model.ThoLink) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Create(values)
}

func (t thoLinkDo) CreateInBatches(values []*model.ThoLink, batchSize int) error {
	return t.DO.CreateInBatches(values, batchSize)
}

// Save : !!! underlying implementation is different with GORM
// The method is equivalent to executing the statement: db.Clauses(clause.OnConflict{UpdateAll: true}).Create(values)
func (t thoLinkDo) Save(values ...*model.ThoLink) error {
	if len(values) == 0 {
		return nil
	}
	return t.DO.Save(values)
}

func (t thoLinkDo) First() (*model.ThoLink, error) {
	if result, err := t.DO.First(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoLink), nil
	}
}

func (t thoLinkDo) Take() (*model.ThoLink, error) {
	if result, err := t.DO.Take(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoLink), nil
	}
}

func (t thoLinkDo) Last() (*model.ThoLink, error) {
	if result, err := t.DO.Last(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoLink), nil
	}
}

func (t thoLinkDo) Find() ([]*model.ThoLink, error) {
	result, err := t.DO.Find()
	return result.([]*model.ThoLink), err
}

func (t thoLinkDo) FindInBatch(batchSize int, fc func(tx gen.Dao, batch int) error) (results []*model.ThoLink, err error) {
	buf := make([]*model.ThoLink, 0, batchSize)
	err = t.DO.FindInBatches(&buf, batchSize, func(tx gen.Dao, batch int) error {
		defer func() { results = append(results, buf...) }()
		return fc(tx, batch)
	})
	return results, err
}

func (t thoLinkDo) FindInBatches(result *[]*model.ThoLink, batchSize int, fc func(tx gen.Dao, batch int) error) error {
	return t.DO.FindInBatches(result, batchSize, fc)
}

func (t thoLinkDo) Attrs(attrs ...field.AssignExpr) IThoLinkDo {
	return t.withDO(t.DO.Attrs(attrs...))
}

func (t thoLinkDo) Assign(attrs ...field.AssignExpr) IThoLinkDo {
	return t.withDO(t.DO.Assign(attrs...))
}

func (t thoLinkDo) Joins(fields ...field.RelationField) IThoLinkDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Joins(_f))
	}
	return &t
}

func (t thoLinkDo) Preload(fields ...field.RelationField) IThoLinkDo {
	for _, _f := range fields {
		t = *t.withDO(t.DO.Preload(_f))
	}
	return &t
}

func (t thoLinkDo) FirstOrInit() (*model.ThoLink, error) {
	if result, err := t.DO.FirstOrInit(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoLink), nil
	}
}

func (t thoLinkDo) FirstOrCreate() (*model.ThoLink, error) {
	if result, err := t.DO.FirstOrCreate(); err != nil {
		return nil, err
	} else {
		return result.(*model.ThoLink), nil
	}
}

func (t thoLinkDo) FindByPage(offset int, limit int) (result []*model.ThoLink, count int64, err error) {
	result, err = t.Offset(offset).Limit(limit).Find()
	if err != nil {
		return
	}

	if size := len(result); 0 < limit && 0 < size && size < limit {
		count = int64(size + offset)
		return
	}

	count, err = t.Offset(-1).Limit(-1).Count()
	return
}

func (t thoLinkDo) ScanByPage(result interface{}, offset int, limit int) (count int64, err error) {
	count, err = t.Count()
	if err != nil {
		return
	}

	err = t.Offset(offset).Limit(limit).Scan(result)
	return
}

func (t thoLinkDo) Scan(result interface{}) (err error) {
	return t.DO.Scan(result)
}

func (t thoLinkDo) Delete(models ...*model.ThoLink) (result gen.ResultInfo, err error) {
	return t.DO.Delete(models)
}

func (t *thoLinkDo) withDO(do gen.Dao) *thoLinkDo {
	t.DO = *do.(*gen.DO)
	return t
}
