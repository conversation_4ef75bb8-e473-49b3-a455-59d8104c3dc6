# Binaries for programs and plugins
*.exe
*.exe~
*.dll
*.so
*.dylib

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool, specifically when used with LiteIDE
*.out

# Dependency directories (remove the comment below to include it)
# vendor/

# Go workspace file
go.work

# Compiled executables (adjust if placed elsewhere)
/rabbitmv_bot
/update_index

# Log files
*.log

# macOS specific files
.DS_Store
._*

# Environment configuration files (if they contain secrets)
# Consider ignoring .env files or similar if used
# rabbitmv_bot.toml # Uncomment if it contains sensitive data and shouldn't be committed

# VS Code settings
.vscode/
